/// <reference types="../vite-env.d.ts" />
import React, { useState, useEffect, useImperativeHandle, forwardRef, useRef, useCallback, memo } from 'react';
import { FolderOpen, Calendar, Download, Eye, Trash2, RefreshCw, Plus, Minus, Search, X, ChevronDown, Cuboid, Video, Image as ImageIcon, Info, RotateCcw, Upload, Box, Wrench } from 'lucide-react';
import { ModelViewer, ModelViewerHandle } from './ModelViewer';
import Masonry from 'react-masonry-css';

// Custom Hook to load a file via IPC and return a data URL
const useDataUrl = (relativePath?: string) => {
  const [url, setUrl] = useState<string | null | undefined>(undefined);

  useEffect(() => {
    let isMounted = true;
    if (relativePath) {
      // Reset url when path changes, to prevent showing old content
      setUrl(undefined); 
      window.electronAPI.loadFile(relativePath).then((dataUrl: string | undefined | null) => {
        if (isMounted) {
          setUrl(dataUrl);
        }
      }).catch((error: Error) => {
        console.error(`useDataUrl: Failed to load ${relativePath}`, error);
        if (isMounted) {
          setUrl(undefined);
        }
      });
    } else {
      setUrl(undefined);
    }
    return () => {
      isMounted = false;
    };
  }, [relativePath]);

  return url;
};

interface Project {
  id: string;
  name: string;
  created_at: string;
  type: 'image-to-3d' | 'text-to-3d' | 'image-generation';
  prompt?: string;
  original_image_path?: string;
  thumbnail_url?: string;
  files?: {
    model?: string;
    video?: string;
    generated_image?: string;
    thumbnail?: string;
  };
  generationStats?: {
    generationMode?: 'text-to-3d' | 'image-to-3d';
    prompt?: string;
    enhancedPrompt?: string;
    imageModel?: string;
    settings?: {
      ss_steps?: number;
      ss_cfg_strength?: number;
      slat_steps?: number;
      slat_cfg_strength?: number;
      seed?: number;
      simplify?: number;
      texture_size?: number;
      enable_lighting_optimizer?: boolean;
      enable_delighter?: boolean;
      delighter_quality?: string;
      width?: number;
      height?: number;
      pipeline?: string;
    };
    fileInfo?: {
      type?: string;
      size?: number;
      vertices?: number;
      faces?: number;
    };
    timing?: {
      totalTime?: number;
      textToImageTime?: number;
      modelGenerationTime?: number;
      delighterTime?: number;
    };
  };
}

interface ProjectsGalleryProps {
  isDarkMode: boolean;
  onCreate3DFromImage: (imageUrl: string, prompt?: string) => void;
}

const ProjectsGallery = forwardRef<any, ProjectsGalleryProps>(({ isDarkMode, onCreate3DFromImage }, ref) => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [thumbSize, setThumbSize] = useState(270); // default 270px
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [isViewerOpen, setIsViewerOpen] = useState(false);
  const [projectToDelete, setProjectToDelete] = useState<Project | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isLoadingDetails, setIsLoadingDetails] = useState(false);
  // --- Add search and filter state ---
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState<'all' | 'image-to-3d' | 'text-to-3d' | 'image-generation'>('all');
  // --- Filtered projects ---
  const filteredProjects = projects.filter(project => {
    const matchesType = typeFilter === 'all' || project.type === typeFilter;
    const matchesSearch =
      project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (project.prompt?.toLowerCase().includes(searchQuery.toLowerCase()) ?? false);
    return matchesType && matchesSearch;
  });

  // Upload functionality
  const imageInputRef = useRef<HTMLInputElement>(null);
  const modelInputRef = useRef<HTMLInputElement>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [uploadType, setUploadType] = useState<'image' | '3d' | null>(null);
  const [uploadFile, setUploadFile] = useState<File | null>(null);
  const [projectName, setProjectName] = useState('');
  const [projectDescription, setProjectDescription] = useState('');
  const [uploadProgress, setUploadProgress] = useState<number | null>(null); // null = indeterminate
  const [pendingModelThumb, setPendingModelThumb] = useState<{ modelFilePath: string, projectId: string } | null>(null);
  const modelViewerRef = useRef<ModelViewerHandle>(null);

  // Memoized fetchProjects to avoid unnecessary re-renders
  // Only call fetchProjects on mount and after project changes (upload/delete/thumbnail),
  // never on modal or input state changes.
  const fetchProjects = useCallback(() => {
    console.log('[ProjectsGallery] fetchProjects called');
    window.electronAPI.getProjects().then(setProjects);
  }, []);

  useEffect(() => {
    fetchProjects(); // Only runs on mount
  }, [fetchProjects]);

  // Debug: log when ProjectsGallery renders
  console.log('[ProjectsGallery] render');

  useImperativeHandle(ref, () => ({
    refresh: fetchProjects,
  }));

  const handleViewDetails = async (project: Project) => {
    if (isLoadingDetails) return; // Prevent multiple clicks

    console.log('View details clicked for project:', project.id);
    setIsLoadingDetails(true);

    try {
      const details = await window.electronAPI.getProjectDetails(project.id);
      console.log('Project details loaded:', details);
      setSelectedProject(details);
      setIsViewerOpen(true);
    } catch (error) {
      console.error('Error loading project details:', error);
      alert('Error loading project details: ' + error);
    } finally {
      setIsLoadingDetails(false);
    }
  };

  const handleDeleteClick = (project: Project) => {
    try {
      console.log('Delete clicked for project:', project.id);
      setProjectToDelete(project);
      setShowDeleteConfirm(true);
    } catch (error) {
      console.error('Error in handleDeleteClick:', error);
      alert('Error opening delete dialog: ' + error);
    }
  };

  const handleDeleteConfirm = async () => {
    if (!projectToDelete) return;

    try {
      setIsDeleting(true);
      await window.electronAPI.deleteProject(projectToDelete.id);
      
      fetchProjects();
      if (selectedProject?.id === projectToDelete.id) {
        setIsViewerOpen(false);
        setSelectedProject(null);
      }
      
      setShowDeleteConfirm(false);
      setProjectToDelete(null);
    } catch (error) {
      console.error('Error deleting project:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteConfirm(false);
    setProjectToDelete(null);
  };

  const handleCloseViewer = () => {
    setIsViewerOpen(false);
    setSelectedProject(null);
  };

  // Upload handlers
  const handleImageUpload = () => {
    setUploadType('image');
    imageInputRef.current?.click();
  };

  const handleModelUpload = () => {
    setUploadType('3d');
    modelInputRef.current?.click();
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>, type: 'image' | '3d') => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (type === 'image' && !file.type.startsWith('image/')) {
      alert('Please select a valid image file.');
      return;
    }

    if (type === '3d') {
      const validExtensions = ['.glb', '.gltf', '.ply', '.obj'];
      const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
      if (!validExtensions.includes(fileExtension)) {
        alert('Please select a valid 3D model file (.glb, .gltf, .ply, .obj).');
        return;
      }
    }

    // Remove file size limit
    // setUploadFile, setUploadType, setProjectName, setShowUploadModal as before
    setUploadFile(file);
    setUploadType(type);
    setProjectName((file.name || '').replace(/\.[^/.]+$/, ''));
    setShowUploadModal(true);
  };

  const handleUploadProject = async () => {
    if (!uploadFile || !projectName.trim()) return;

    setIsUploading(true);
    setUploadProgress(0);
    try {
      if (uploadType === 'image') {
        // Convert image to base64
        const reader = new FileReader();
        reader.onloadstart = () => {
          setUploadProgress(null); // Indeterminate
          console.log('[Upload] FileReader onloadstart');
        };
        reader.onload = async (e) => {
          try {
            console.log('[Upload] FileReader onload', e);
            const base64Data = (e.target?.result as string).split(',')[1]; // Remove data URL prefix
            console.log('[Upload] base64Data length:', base64Data?.length);
            setUploadProgress(80); // Simulate progress
            // FIX: Pass image_base64 at top level, remove modelFilePath, videoFilePath, originalImagePath, generationStats
            if (!window.electronAPI.createProjectFromImage || typeof window.electronAPI.createProjectFromImage !== 'function') {
              console.error('[Upload] createProjectFromImage is not defined on electronAPI');
              alert('createProjectFromImage is not available.');
              setIsUploading(false);
              return;
            }
            const payload = {
              name: projectName.trim(),
              type: 'image-generation',
              image_base64: base64Data,
              prompt: projectDescription.trim() || 'Uploaded image',
              settings: {
                description: projectDescription.trim()
              }
            };
            console.log('[Upload] Calling createProjectFromImage with:', payload);
            const result = await window.electronAPI.createProjectFromImage(payload);
            console.log('[Upload] createProjectFromImage result:', result);
            setUploadProgress(100);
            setTimeout(() => setUploadProgress(null), 500);
            if (result.success) {
              await fetchProjects(); // Refresh projects list
              setShowUploadModal(false);
              resetUploadState();
            } else {
              throw new Error(result.error || 'Failed to create project');
            }
          } catch (error) {
            setUploadProgress(null);
            const errMsg = (typeof error === 'object' && error && 'message' in error) ? (error as any).message : String(error);
            console.error('[Upload] Error creating image project:', error);
            alert('Failed to create project from image: ' + errMsg);
          } finally {
            setIsUploading(false);
          }
        };
        reader.readAsDataURL(uploadFile);
      } else if (uploadType === '3d') {
        // 3D model upload with progress
        try {
          // Use XMLHttpRequest for progress if possible, else fallback to IPC
          const buffer = await uploadFile.arrayBuffer();
          const total = buffer.byteLength;
          let loaded = 0;
          // Simulate chunked upload with progress
          const chunkSize = 1024 * 1024 * 2; // 2MB
          let offset = 0;
          let modelFilePath = null;
          while (offset < total) {
            const chunk = buffer.slice(offset, offset + chunkSize);
            // Send chunk via IPC (simulate, as current API expects full file)
            // In real implementation, backend should support chunked upload
            // For now, just update progress
            loaded += chunk.byteLength;
            setUploadProgress(Math.min(99, Math.round((loaded / total) * 100)));
            offset += chunkSize;
          }
          // Actually upload the file (full buffer)
          modelFilePath = await window.electronAPI.saveUploadedModelFile(uploadFile as File);
          setUploadProgress(99);
          const result = await window.electronAPI.createProjectFrom3DModel({
            name: projectName.trim(),
            type: 'image-to-3d',
            modelFilePath,
            videoFilePath: undefined,
            originalImagePath: undefined,
            prompt: projectDescription.trim() || 'Uploaded 3D model',
            settings: {
              description: projectDescription.trim()
            },
            generationStats: {
              model: 'uploaded',
            }
          });
          setUploadProgress(100);
          setTimeout(() => setUploadProgress(null), 500);
          if (result.success && result.project && typeof result.project.id === 'string' && typeof modelFilePath === 'string') {
            // After project is created, trigger thumbnail capture
            setPendingModelThumb({ modelFilePath: modelFilePath || '', projectId: result.project.id || '' });
            await fetchProjects();
            setShowUploadModal(false);
            resetUploadState();
          } else {
            throw new Error(result.error || 'Failed to create project');
          }
        } catch (error) {
          setUploadProgress(null);
          console.error('Error creating 3D model project:', error);
          alert('Failed to create project from 3D model');
        } finally {
          setIsUploading(false);
        }
      }
    } catch (error) {
      setUploadProgress(null);
      console.error('Error uploading project:', error);
      alert('Failed to upload project');
      setIsUploading(false);
    }
  };

  const resetUploadState = () => {
    setUploadFile(null);
    setUploadType(null);
    setProjectName('');
    setProjectDescription('');
    if (imageInputRef.current) imageInputRef.current.value = '';
    if (modelInputRef.current) modelInputRef.current.value = '';
  };

  const cancelUpload = () => {
    setShowUploadModal(false);
    resetUploadState();
  };

  const ProjectCard = ({ project }: { project: Project }) => (
    <div className={`group relative rounded-lg overflow-hidden shadow-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} hover:shadow-xl transition-shadow duration-300 flex flex-col`} style={{ width: thumbSize }}>
      <div className="relative">
        <ProjectThumbnail project={project} />
        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div className="absolute inset-0 bg-black bg-opacity-50 z-10 pointer-events-none" />
          <button
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleViewDetails(project);
            }}
            disabled={isLoadingDetails}
            className={`relative z-20 text-white rounded-full p-3 transition-colors cursor-pointer pointer-events-auto ${
              isLoadingDetails
                ? 'bg-blue-400 cursor-not-allowed'
                : 'bg-blue-500 hover:bg-blue-600'
            }`}
            type="button"
          >
            <Eye size={24} />
          </button>
        </div>
      </div>
      <div className="p-3">
        <h3 className="text-lg font-semibold text-gray-800 dark:text-white truncate">{project.name}</h3>
        <p className="text-sm text-gray-500 dark:text-gray-400 capitalize">{project.type.replace('-', ' ')}</p>
      </div>
      <div className="px-3 pb-3 flex justify-between items-center text-xs text-gray-400 dark:text-gray-500">
        <div className="flex items-center">
          <Calendar size={14} className="mr-1" />
          <span>{new Date(project.created_at).toLocaleDateString()}</span>
        </div>
        <button
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            handleDeleteClick(project);
          }}
          className="text-red-500 hover:text-red-700 transition-colors cursor-pointer"
          type="button"
        >
          <Trash2 size={16} />
        </button>
      </div>
    </div>
  );

  const ProjectThumbnail = ({ project }: { project: Project }) => {
    const thumbnailUrl = useDataUrl(project.thumbnail_url);
    return (
      <div className={`${isDarkMode ? 'bg-gray-600' : 'bg-gray-200'} flex items-center justify-center overflow-hidden`}>
        {thumbnailUrl ? (
          <img
            src={thumbnailUrl}
            alt={project.name}
            className="w-full h-auto group-hover:scale-105 transition-transform duration-300"
          />
        ) : (
          <Cuboid className={`w-16 h-16 ${isDarkMode ? 'text-gray-500' : 'text-gray-400'}`} />
        )}
      </div>
    );
  };

  const ImageViewerContent = ({ project }: { project: Project }) => {
    const generatedImageUrl = useDataUrl(project.files?.generated_image);
    const [showInfo, setShowInfo] = useState(false);
    // --- Tool Menu and Upscale Modal State ---
    const [showToolMenu, setShowToolMenu] = useState(false);
    const [showUpscaleModal, setShowUpscaleModal] = useState(false);
    const [zoomLevel, setZoomLevel] = useState(1);
    const [panPosition, setPanPosition] = useState({ x: 0, y: 0 });
    const [isDragging, setIsDragging] = useState(false);
    const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
    const imageRef = useRef<HTMLImageElement>(null);

    const handleZoomIn = () => {
      setZoomLevel(prev => Math.min(prev * 1.5, 5)); // Max 5x zoom
    };

    const handleZoomOut = () => {
      setZoomLevel(prev => Math.max(prev / 1.5, 0.5)); // Min 0.5x zoom
    };

    const handleResetZoom = () => {
      setZoomLevel(1);
      setPanPosition({ x: 0, y: 0 });
    };

    const handleMouseDown = (e: React.MouseEvent) => {
      if (zoomLevel > 1) {
        setIsDragging(true);
        setDragStart({ x: e.clientX - panPosition.x, y: e.clientY - panPosition.y });
      }
    };

    const handleMouseMove = (e: React.MouseEvent) => {
      if (isDragging && zoomLevel > 1) {
        setPanPosition({
          x: e.clientX - dragStart.x,
          y: e.clientY - dragStart.y
        });
      }
    };

    const handleMouseUp = () => {
      setIsDragging(false);
    };

    const handleWheel = (e: React.WheelEvent) => {
      e.preventDefault();
      if (e.deltaY < 0) {
        handleZoomIn();
      } else {
        handleZoomOut();
      }
    };

    return (
        <div className="w-full h-full flex flex-col">
            {/* Image Display Area - Takes most of the space */}
            <div
              className="relative flex-1 bg-gray-900 rounded-lg overflow-hidden flex items-center justify-center cursor-grab"
              style={{ minHeight: 'calc(100vh - 250px)' }}
              onMouseDown={handleMouseDown}
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
              onMouseLeave={handleMouseUp}
              onWheel={handleWheel}
            >
                {generatedImageUrl ? (
                    <img
                        ref={imageRef}
                        src={generatedImageUrl}
                        alt={project.name}
                        className="max-w-full max-h-full object-contain transition-transform duration-200"
                        style={{
                          transform: `scale(${zoomLevel}) translate(${panPosition.x / zoomLevel}px, ${panPosition.y / zoomLevel}px)`,
                          cursor: isDragging ? 'grabbing' : zoomLevel > 1 ? 'grab' : 'default'
                        }}
                        draggable={false}
                    />
                ) : (
                    <div className="text-white">Image not found</div>
                )}

                {/* Zoom Controls */}
                <div className="absolute top-4 left-4 flex flex-col gap-2 z-20">
                  <button
                    onClick={handleZoomIn}
                    className="bg-gray-800 bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75 transition-all"
                    title="Zoom In"
                  >
                    <Plus size={20} />
                  </button>
                  <button
                    onClick={handleZoomOut}
                    className="bg-gray-800 bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75 transition-all"
                    title="Zoom Out"
                  >
                    <Minus size={20} />
                  </button>
                  <button
                    onClick={handleResetZoom}
                    className="bg-gray-800 bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75 transition-all"
                    title="Reset Zoom"
                  >
                    <RotateCcw size={20} />
                  </button>
                  <div className="bg-gray-800 bg-opacity-50 text-white px-2 py-1 rounded text-sm">
                    {Math.round(zoomLevel * 100)}%
                  </div>
                </div>

                {/* Info and Tools Icons */}
                <div className="absolute top-4 right-4 flex flex-col gap-2 z-30">
                  {!showInfo && (
                    <button
                      onClick={() => setShowInfo(!showInfo)}
                      className="bg-gray-800 bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75"
                      title="View project info"
                    >
                      <Info size={24} />
                    </button>
                  )}
                  {/* Tools Icon */}
                  <button
                    onClick={() => setShowToolMenu(v => !v)}
                    className="bg-gray-800 bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75"
                    title="Tools"
                  >
                    <Wrench size={22} />
                  </button>
                  {/* Tool Menu Dropdown */}
                  {showToolMenu && (
                    <div className={`absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg z-40 border border-gray-200 dark:border-gray-700`}>
                      <button
                        className="w-full text-left px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-700 text-sm font-medium text-gray-800 dark:text-gray-200 rounded-t-lg"
                        onClick={() => { setShowToolMenu(false); setShowUpscaleModal(true); }}
                      >
                        Upscale Image
                      </button>
                      {/* Add more tool options here in the future */}
                    </div>
                  )}
                </div>

                {/* Upscale Modal */}
                {showUpscaleModal && (
                  <UpscaleModal
                    imageUrl={generatedImageUrl}
                    onClose={() => setShowUpscaleModal(false)}
                    project={project}
                    isDarkMode={isDarkMode}
                    // Add more props as needed
                  />
                )}
            </div>

            {/* Action Buttons Area */}
            {project.type === 'image-generation' && generatedImageUrl && (
              <div className="flex justify-center gap-3 mt-4 p-4 bg-gray-800 rounded-lg">
                <button
                  onClick={() => {
                    if (generatedImageUrl) {
                      onCreate3DFromImage(generatedImageUrl, project.prompt);
                      handleCloseViewer();
                    }
                  }}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2"
                  title="Create 3D Model from this Image"
                >
                  <Cuboid size={20} />
                  Create 3D Model
                </button>
                <button
                  onClick={() => {
                    if (generatedImageUrl) {
                      const link = document.createElement('a');
                      link.href = generatedImageUrl;
                      link.download = `${project.name || 'image'}.png`;
                      document.body.appendChild(link);
                      link.click();
                      document.body.removeChild(link);
                    }
                  }}
                  className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center gap-2"
                  title="Save Image"
                >
                  <Download size={20} />
                  Save Image
                </button>
              </div>
            )}

            {showInfo && (
                <div 
                  className={`absolute top-0 right-0 h-full ${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-l-lg shadow-lg w-96 text-sm overflow-y-auto z-10 transition-transform transform translate-x-0`}
                  onClick={e => e.stopPropagation()}
                >
                    <div className="flex justify-between items-center mb-4">
                      <h3 className={`font-bold text-xl ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>{project.name}</h3>
                      <button onClick={() => setShowInfo(false)} className={`${isDarkMode ? 'text-gray-400 hover:text-white' : 'text-gray-500 hover:text-gray-800'}`}>
                        <X size={20}/>
                      </button>
                    </div>
                    
                    {project.prompt && (
                        <div>
                            <h4 className={`font-semibold mt-4 mb-2 ${isDarkMode ? 'text-gray-100' : 'text-gray-800'}`}>Prompt</h4>
                            <p className={`${isDarkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-600'} p-3 rounded-md whitespace-pre-wrap`}>{project.prompt}</p>
                        </div>
                    )}
                     {project.generationStats?.settings && (
                        <div>
                          <h4 className={`font-semibold mt-4 mb-2 ${isDarkMode ? 'text-gray-100' : 'text-gray-800'}`}>Generation Settings</h4>
                          <div className={`text-sm space-y-2 ${isDarkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-600'} p-3 rounded-md`}>
                            {Object.entries(project.generationStats.settings).map(([key, value]) => (
                              <div key={key} className="flex justify-between">
                                <strong className="capitalize">{key.replace(/_/g, ' ')}:</strong> 
                                <span>{value.toString()}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                      <div className="mt-4">
                        <h4 className={`font-semibold mb-2 ${isDarkMode ? 'text-gray-100' : 'text-gray-800'}`}>Details</h4>
                         <div className={`text-sm space-y-2 ${isDarkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-600'} p-3 rounded-md`}>
                           <div className="flex justify-between">
                              <strong>Type:</strong>
                              <span className="capitalize">{project.type.replace('-', ' ')}</span>
                            </div>
                            <div className="flex justify-between">
                              <strong>Created:</strong>
                              <span>{new Date(project.created_at).toLocaleString()}</span>
                           </div>

                           {/* Image Model Used */}
                           {project.generationStats?.imageModel && (
                             <div className="flex justify-between">
                               <strong>Image Model:</strong>
                               <span>{project.generationStats.imageModel}</span>
                             </div>
                           )}

                           {/* Resolution - try to get from settings */}
                           {typeof project.generationStats?.settings?.width === 'number' && typeof project.generationStats?.settings?.height === 'number' && (
                             <div className="flex justify-between">
                               <strong>Resolution:</strong>
                               <span>{project.generationStats.settings.width} × {project.generationStats.settings.height}</span>
                             </div>
                           )}

                           {/* File Size if available */}
                           {project.generationStats?.fileInfo?.size && (
                             <div className="flex justify-between">
                               <strong>File Size:</strong>
                               <span>{(project.generationStats.fileInfo.size / 1024 / 1024).toFixed(2)} MB</span>
                             </div>
                           )}
                        </div>
                      </div>
                </div>
            )}
        </div>
    );
  };

  const ViewerContent = ({ project }: { project: Project }) => {
    const originalImageUrl = useDataUrl(project.original_image_path);
    const modelUrl = useDataUrl(project.files?.model);
    const videoUrl = useDataUrl(project.files?.video);
    const generatedImageUrl = useDataUrl(project.files?.generated_image);
    const [showInfo, setShowInfo] = useState(false);

    // Zoom functionality for images
    const [zoomLevel, setZoomLevel] = useState(1);
    const [panPosition, setPanPosition] = useState({ x: 0, y: 0 });
    const [isDragging, setIsDragging] = useState(false);
    const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

    const handleZoomIn = () => {
      setZoomLevel(prev => Math.min(prev * 1.5, 5));
    };

    const handleZoomOut = () => {
      setZoomLevel(prev => Math.max(prev / 1.5, 0.5));
    };

    const handleResetZoom = () => {
      setZoomLevel(1);
      setPanPosition({ x: 0, y: 0 });
    };

    const handleMouseDown = (e: React.MouseEvent) => {
      if (zoomLevel > 1) {
        setIsDragging(true);
        setDragStart({ x: e.clientX - panPosition.x, y: e.clientY - panPosition.y });
      }
    };

    const handleMouseMove = (e: React.MouseEvent) => {
      if (isDragging && zoomLevel > 1) {
        setPanPosition({
          x: e.clientX - dragStart.x,
          y: e.clientY - dragStart.y
        });
      }
    };

    const handleMouseUp = () => {
      setIsDragging(false);
    };

    const handleWheel = (e: React.WheelEvent) => {
      e.preventDefault();
      if (e.deltaY < 0) {
        handleZoomIn();
      } else {
        handleZoomOut();
      }
    };

    if (project.type === 'image-generation') {
        return <ImageViewerContent project={project} />;
    }

    // Don't render the model viewer until the URL is loaded
    if (!modelUrl && !videoUrl && !generatedImageUrl) {
      return (
        <div className={`w-full min-h-[60vh] max-h-[70vh] flex items-center justify-center ${isDarkMode ? 'bg-gray-800' : 'bg-gray-900'} rounded-lg`}>
          <div className="text-center text-white">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
            <p>Loading asset...</p>
          </div>
        </div>
      );
    }

    const modelViewerNode = modelUrl ? (
      <ModelViewer
        isTextured={true}
        isDarkMode={isDarkMode}
        modelUrl={modelUrl}
        filePath={project.files?.model}
        generationStats={project.generationStats || {}}
      />
    ) : null;

    return (
      <div className="w-full h-full flex flex-col">
        {/* Content Display Area - Takes up most of the space */}
        <div className="relative flex-1 bg-gray-900 rounded-lg overflow-hidden" style={{ minHeight: 'calc(100vh - 200px)' }}>
          {videoUrl ? (
            <video
              src={videoUrl}
              autoPlay
              loop
              muted
              controls
              className="w-full h-full object-contain"
            />
          ) : modelViewerNode ? (
            <div className="w-full h-full">
              {modelViewerNode}
            </div>
          ) : generatedImageUrl ? (
            <div
              className="w-full h-full flex items-center justify-center cursor-grab"
              onMouseDown={handleMouseDown}
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
              onMouseLeave={handleMouseUp}
              onWheel={handleWheel}
            >
              <img
                src={generatedImageUrl}
                alt={project.name}
                className="max-w-full max-h-full object-contain transition-transform duration-200"
                style={{
                  transform: `scale(${zoomLevel}) translate(${panPosition.x / zoomLevel}px, ${panPosition.y / zoomLevel}px)`,
                  cursor: isDragging ? 'grabbing' : zoomLevel > 1 ? 'grab' : 'default'
                }}
                draggable={false}
              />

              {/* Zoom Controls for Images */}
              <div className="absolute top-4 left-4 flex flex-col gap-2 z-20">
                <button
                  onClick={handleZoomIn}
                  className="bg-gray-800 bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75 transition-all"
                  title="Zoom In"
                >
                  <Plus size={20} />
                </button>
                <button
                  onClick={handleZoomOut}
                  className="bg-gray-800 bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75 transition-all"
                  title="Zoom Out"
                >
                  <Minus size={20} />
                </button>
                <button
                  onClick={handleResetZoom}
                  className="bg-gray-800 bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75 transition-all"
                  title="Reset Zoom"
                >
                  <RotateCcw size={20} />
                </button>
                <div className="bg-gray-800 bg-opacity-50 text-white px-2 py-1 rounded text-sm">
                  {Math.round(zoomLevel * 100)}%
                </div>
              </div>
            </div>
          ) : (
            <div className="w-full h-full flex items-center justify-center text-white">
              Asset not found
            </div>
          )}

          {!showInfo && (
              <button
                  onClick={() => setShowInfo(!showInfo)}
                  className="absolute top-4 right-4 bg-gray-800 bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75 z-20"
                  title="View project info"
              >
                  <Info size={24} />
              </button>
          )}
        </div>

        {showInfo && (
            <div 
              className={`absolute top-0 right-0 h-full ${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-l-lg shadow-lg w-96 text-sm overflow-y-auto z-10 transition-transform transform translate-x-0`}
              onClick={e => e.stopPropagation()}
            >
                <div className="flex justify-between items-center mb-4">
                  <h3 className={`font-bold text-xl ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>{project.name}</h3>
                  <button onClick={() => setShowInfo(false)} className={`${isDarkMode ? 'text-gray-400 hover:text-white' : 'text-gray-500 hover:text-gray-800'}`}>
                    <X size={20}/>
                  </button>
                </div>
                
                {project.prompt && (
                    <div>
                        <h4 className={`font-semibold mt-4 mb-2 ${isDarkMode ? 'text-gray-100' : 'text-gray-800'}`}>Prompt</h4>
                        <p className={`${isDarkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-600'} p-3 rounded-md whitespace-pre-wrap`}>{project.prompt}</p>
                    </div>
                )}
                 {project.generationStats?.settings && (
                    <div>
                      <h4 className={`font-semibold mt-4 mb-2 ${isDarkMode ? 'text-gray-100' : 'text-gray-800'}`}>Inference Settings</h4>
                      <div className={`text-sm space-y-2 ${isDarkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-600'} p-3 rounded-md`}>
                        {/* Sparse Structure Settings */}
                        {project.generationStats.settings.ss_steps && (
                          <div className="flex justify-between">
                            <strong className={isDarkMode ? 'text-gray-200' : 'text-gray-800'}>SS Steps:</strong>
                            <span className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>{project.generationStats.settings.ss_steps}</span>
                          </div>
                        )}
                        {project.generationStats.settings.ss_cfg_strength && (
                          <div className="flex justify-between">
                            <strong className={isDarkMode ? 'text-gray-200' : 'text-gray-800'}>SS CFG Strength:</strong>
                            <span className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>{project.generationStats.settings.ss_cfg_strength}</span>
                          </div>
                        )}
                        {/* SLAT Settings */}
                        {project.generationStats.settings.slat_steps && (
                          <div className="flex justify-between">
                            <strong className={isDarkMode ? 'text-gray-200' : 'text-gray-800'}>SLAT Steps:</strong>
                            <span className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>{project.generationStats.settings.slat_steps}</span>
                          </div>
                        )}
                        {project.generationStats.settings.slat_cfg_strength && (
                          <div className="flex justify-between">
                            <strong className={isDarkMode ? 'text-gray-200' : 'text-gray-800'}>SLAT CFG Strength:</strong>
                            <span className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>{project.generationStats.settings.slat_cfg_strength}</span>
                          </div>
                        )}
                        {/* Other Settings */}
                        {project.generationStats.settings.seed && (
                          <div className="flex justify-between">
                            <strong className={isDarkMode ? 'text-gray-200' : 'text-gray-800'}>Seed:</strong>
                            <span className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>{project.generationStats.settings.seed}</span>
                          </div>
                        )}
                        {project.generationStats.settings.simplify && (
                          <div className="flex justify-between">
                            <strong className={isDarkMode ? 'text-gray-200' : 'text-gray-800'}>Simplify Ratio:</strong>
                            <span className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>{project.generationStats.settings.simplify}</span>
                          </div>
                        )}
                        {typeof project.generationStats.settings.enable_lighting_optimizer === 'boolean' && (
                          <div className="flex justify-between">
                            <strong className={isDarkMode ? 'text-gray-200' : 'text-gray-800'}>Lighting Optimizer:</strong>
                            <span className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>{project.generationStats.settings.enable_lighting_optimizer ? 'Enabled' : 'Disabled'}</span>
                          </div>
                        )}
                        {typeof project.generationStats.settings.enable_delighter === 'boolean' && (
                          <div className="flex justify-between">
                            <strong className={isDarkMode ? 'text-gray-200' : 'text-gray-800'}>Delighter:</strong>
                            <span className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>{project.generationStats.settings.enable_delighter ? 'Enabled' : 'Disabled'}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                  <div className="mt-4">
                    <h4 className={`font-semibold mb-2 ${isDarkMode ? 'text-gray-100' : 'text-gray-800'}`}>Details</h4>
                     <div className={`text-sm space-y-2 ${isDarkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-600'} p-3 rounded-md`}>
                       <div className="flex justify-between">
                          <strong className={isDarkMode ? 'text-gray-200' : 'text-gray-800'}>Type:</strong>
                          <span className={`capitalize ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>{project.type.replace('-', ' ')}</span>
                        </div>
                        <div className="flex justify-between">
                          <strong className={isDarkMode ? 'text-gray-200' : 'text-gray-800'}>Created:</strong>
                          <span className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>{new Date(project.created_at).toLocaleString()}</span>
                       </div>
                       {/* Model Used */}
                       {typeof project.generationStats?.settings?.pipeline === 'string' && (
                         <div className="flex justify-between">
                           <strong className={isDarkMode ? 'text-gray-200' : 'text-gray-800'}>Model Used:</strong>
                           <span className={`capitalize ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                             {project.generationStats.settings.pipeline}
                           </span>
                         </div>
                       )}
                       {project.generationStats?.settings && typeof project.generationStats.settings.pipeline !== 'string' && (
                         <div className="flex justify-between">
                           <strong className={isDarkMode ? 'text-gray-200' : 'text-gray-800'}>Model Used:</strong>
                           <span className={`capitalize ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>Trellis</span>
                         </div>
                       )}
                       {/* Texture Resolution */}
                       {project.generationStats?.settings?.texture_size && (
                         <div className="flex justify-between">
                           <strong className={isDarkMode ? 'text-gray-200' : 'text-gray-800'}>Texture Resolution:</strong>
                           <span className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>{project.generationStats.settings.texture_size}x{project.generationStats.settings.texture_size}</span>
                         </div>
                       )}
                    </div>
                  </div>
                  <div className="mt-4">
                    <h4 className={`font-semibold mb-2 ${isDarkMode ? 'text-gray-100' : 'text-gray-800'}`}>Assets</h4>
                    <div className="space-y-2">
                        {originalImageUrl && <AssetItem icon={<ImageIcon/>} label="Original Image" url={originalImageUrl} onDownload={() => { if(project.original_image_path) window.electronAPI.downloadFile(project.original_image_path)}} />}
                        {modelUrl && <AssetItem icon={<Cuboid/>} label="3D Model (.glb)" url={modelUrl} onDownload={() => { if(project.files?.model) window.electronAPI.downloadFile(project.files.model)}} />}
                        {videoUrl && <AssetItem icon={<Video/>} label="Turntable Video (.mp4)" url={videoUrl} onDownload={() => { if(project.files?.video) window.electronAPI.downloadFile(project.files.video)}} />}
                    </div>
                  </div>
            </div>
        )}
      </div>
    );
  };
  
  const formatFileSize = (bytes?: number) => {
    if (!bytes || bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };
  
  const AssetItem = ({ icon, label, url, onDownload }: { icon: React.ReactNode, label: string, url: string, onDownload: () => void }) => (
    <div className="flex items-center space-x-2 p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
        <div className="text-gray-500 dark:text-gray-400">{icon}</div>
        <div className="flex-grow">
            <p className="font-medium">{label}</p>
        </div>
        <a onClick={onDownload} className="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 cursor-pointer">
            <Download size={18}/>
        </a>
    </div>
  );

  // --- UpscaleModal Stub ---
  interface UpscaleModalProps {
    imageUrl: string | null | undefined;
    onClose: () => void;
    project: Project;
    isDarkMode: boolean;
  }



  const UpscaleModal: React.FC<UpscaleModalProps> = ({ imageUrl, onClose, project, isDarkMode }) => {
    // Core upscaling states
    const [isUpscaling, setIsUpscaling] = useState(false); // Don't auto-start
    const [progress, setProgress] = useState(0);
    const [upscaledUrl, setUpscaledUrl] = useState<string | null>(null);
    const [showSlider, setShowSlider] = useState(false);
    const [sliderX, setSliderX] = useState(0.5); // 0 (left) to 1 (right)
    const sliderRef = useRef<HTMLDivElement>(null);
    const [isDragging, setIsDragging] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [progressMsg, setProgressMsg] = useState<string | null>(null);

    // Model and settings states (Upscayl-inspired)
    const [availableModels, setAvailableModels] = useState<string[]>([]);
    const [selectedModel, setSelectedModel] = useState<string>('swinir-real-sr-x4');
    const [scaleFactor, setScaleFactor] = useState<number>(4);
    const [compression, setCompression] = useState<number>(0);
    const [imageFormat, setImageFormat] = useState<string>('png');
    const [showAdvanced, setShowAdvanced] = useState(false);
    const [gpuId, setGpuId] = useState<string>('');
    const [customWidth, setCustomWidth] = useState<number>(0);
    const [useCustomWidth, setUseCustomWidth] = useState<boolean>(false);
    const [tileSize, setTileSize] = useState<number>(400);
    const [ttaMode, setTtaMode] = useState<boolean>(false);

    useEffect(() => {
      // Fetch available models from backend
      if (window.electronAPI.getUpscaleModels) {
        window.electronAPI.getUpscaleModels().then((models: string[]) => {
          console.log('[UpscaleModal] Received models from backend:', models);
          // Show all models - let the backend handle compatibility
          setAvailableModels(models.length > 0 ? models : ['swinir-real-sr-x4']);
          if (models.length > 0) setSelectedModel(models[0]);
        }).catch((error) => {
          console.error('[UpscaleModal] Error fetching models:', error);
          setAvailableModels(['swinir-real-sr-x4']);
        });
      } else {
        console.log('[UpscaleModal] getUpscaleModels API not available');
        setAvailableModels(['swinir-real-sr-x4']);
      }
    }, []);

    // Listen for progress events
    useEffect(() => {
      function onProgress(data: any) {
        setProgress(data.progress || 0);
        setProgressMsg(data.message || null);
      }
      const remove = window.electronAPI.on('upscale-image-progress', onProgress);
      return () => {
        remove();
      };
    }, []);

    // Helper to get a temp output path
    async function getTempOutputPath() {
      let ext = 'png'; // default extension

      if (imageUrl) {
        if (imageUrl.startsWith('data:')) {
          // Extract extension from data URL mime type
          const mimeMatch = imageUrl.match(/^data:image\/([a-zA-Z]+);/);
          if (mimeMatch) {
            ext = mimeMatch[1] === 'jpeg' ? 'jpg' : mimeMatch[1];
          }
        } else {
          // Extract extension from file path
          const pathExt = imageUrl.split('.').pop();
          if (pathExt && pathExt.length <= 4) {
            ext = pathExt;
          }
        }
      }

      const base = `upscaled_${Date.now()}.${ext}`;
      return window.electronAPI.getTempFilePath ? await window.electronAPI.getTempFilePath(base) : base;
    }

    // Main upscaling logic
    const handleApply = async () => {
      console.log('[UpscaleModal] BUTTON CLICKED - Starting upscale process...');
      setIsUpscaling(true);
      setProgress(0);
      setShowSlider(false);
      setUpscaledUrl(null);
      setError(null);
      setProgressMsg(null);
      try {
        let inputPath = imageUrl;
        console.log('[UpscaleModal] Original imageUrl:', imageUrl?.substring(0, 100) + '...');
        // If it's not a file path, always write to temp file
        const isFilePath = typeof inputPath === 'string' &&
          (inputPath.startsWith('/') || /^[A-Z]:[\\/]/i.test(inputPath));
        console.log('[UpscaleModal] Is file path?', isFilePath);
        if (!isFilePath) {
          // If it's a data URL, use as is; if it's a base64 string, convert to data URL
          let dataUrl = inputPath;
          if (dataUrl && !dataUrl.startsWith('data:')) {
            // Assume PNG if not specified
            dataUrl = `data:image/png;base64,${dataUrl}`;
          }
          console.log('[UpscaleModal] Converting to temp file...');
          inputPath = await window.electronAPI.saveDataUrlToTempFile(dataUrl || '');
          console.log('[UpscaleModal] Converted to temp file:', inputPath);
        }
        // Always await getTempOutputPath
        console.log('[UpscaleModal] Getting temp output path...');
        let outputPath;
        try {
          outputPath = await getTempOutputPath();
          console.log('[UpscaleModal] Output path:', outputPath);
        } catch (outputError) {
          console.error('[UpscaleModal] Error getting output path:', outputError);
          throw new Error('Failed to get output path: ' + outputError.message);
        }
        // Double-check inputPath and outputPath are both short file paths
        if (!inputPath || typeof inputPath !== 'string' || inputPath.length > 260 || inputPath.startsWith('data:')) {
          throw new Error('Input path is not a valid file path: ' + inputPath);
        }
        if (!outputPath || typeof outputPath !== 'string' || outputPath.length > 260) {
          throw new Error('Output path is not a valid file path: ' + outputPath);
        }
        console.log('[UpscaleModal] Calling upscale-image with:', {
          input: inputPath,
          output: outputPath,
          model: selectedModel,
          scale: scaleFactor,
          compression: compression,
          format: imageFormat,
          gpuId: gpuId || null,
          customWidth: useCustomWidth ? customWidth : null,
          tileSize: tileSize,
          ttaMode: ttaMode
        });
        const result = await window.electronAPI.invoke('upscale-image', {
          input: inputPath,
          output: outputPath,
          model: selectedModel,
          scale: scaleFactor,
          compression: compression,
          format: imageFormat,
          gpuId: gpuId || null,
          customWidth: useCustomWidth ? customWidth : null,
          tileSize: tileSize,
          ttaMode: ttaMode
        });
        console.log('[UpscaleModal] Upscale result:', result);
        if (result.success) {
          // Load the upscaled image file as a data URL for display
          try {
            // For temp files, use the filename directly since they're in the temp directory
            let loadPath = result.output;

            // Check if this is a temp file
            if (loadPath.includes('\\temp\\') || loadPath.includes('/temp/')) {
              // Extract just the filename for temp files
              const filename = loadPath.split(/[\\\/]/).pop();
              loadPath = `temp/${filename}`;
              console.log('[UpscaleModal] Using temp file path:', loadPath);
            } else {
              // Convert absolute path to relative path for other files
              const appRoot = await window.electronAPI.getAppRoot();
              if (loadPath.startsWith(appRoot)) {
                loadPath = loadPath.substring(appRoot.length + 1); // +1 for the path separator
              }
              console.log('[UpscaleModal] Using relative path:', loadPath);
            }

            console.log('[UpscaleModal] Loading upscaled image from:', loadPath);
            console.log('[UpscaleModal] Original result path:', result.output);

            try {
              const upscaledDataUrl = await window.electronAPI.loadFile(loadPath);
              if (upscaledDataUrl) {
                console.log('[UpscaleModal] Successfully loaded upscaled image');
                setUpscaledUrl(upscaledDataUrl);
                setShowSlider(true);
              } else {
                console.error('[UpscaleModal] Failed to load upscaled image - no data returned');
                setError('Failed to load upscaled image for display.');
              }
            } catch (loadError) {
              console.error('[UpscaleModal] Error loading upscaled image:', loadError);
              setError(`Failed to load upscaled image: ${loadError.message}`);
            }
          } catch (loadError) {
            console.error('[UpscaleModal] Error loading upscaled image:', loadError);
            setError('Failed to load upscaled image for display.');
          }
        } else {
          setError(result.error || 'Upscaling failed.');
        }
      } catch (err: any) {
        console.error('[UpscaleModal] Error during upscaling:', err);
        setError(err?.message || 'Upscaling failed.');
      } finally {
        console.log('[UpscaleModal] Upscaling process finished');
        setIsUpscaling(false);
      }
    };

    // Slider drag logic
    const handleSliderDown = (e: React.MouseEvent) => {
      setIsDragging(true);
    };
    const handleSliderMove = (e: React.MouseEvent) => {
      if (!isDragging || !sliderRef.current) return;
      const rect = sliderRef.current.getBoundingClientRect();
      let x = (e.clientX - rect.left) / rect.width;
      x = Math.max(0, Math.min(1, x));
      setSliderX(x);
    };
    const handleSliderUp = () => setIsDragging(false);

    useEffect(() => {
      if (!isDragging) return;
      const move = (e: MouseEvent) => handleSliderMove(e as any);
      const up = () => setIsDragging(false);
      window.addEventListener('mousemove', move);
      window.addEventListener('mouseup', up);
      return () => {
        window.removeEventListener('mousemove', move);
        window.removeEventListener('mouseup', up);
      };
    }, [isDragging]);

    // Save/Download logic
    const handleSave = async () => {
      if (!upscaledUrl) return;

      try {
        // Create a download link
        const link = document.createElement('a');
        link.href = upscaledUrl;

        // Generate filename based on original project name and settings
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const filename = `${project.name}_upscaled_${scaleFactor}x_${selectedModel}_${timestamp}.${imageFormat}`;
        link.download = filename;

        // Trigger download
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        console.log(`Downloaded upscaled image: ${filename}`);
      } catch (error) {
        console.error('Error downloading upscaled image:', error);
        setError('Failed to download upscaled image');
      }
    };

    return (
      <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4">
        <div className={`relative bg-white dark:bg-gray-900 rounded-lg shadow-xl max-w-5xl w-full max-h-[90vh] overflow-hidden flex ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
          <button
            onClick={onClose}
            className="absolute top-4 right-4 text-gray-400 hover:text-gray-700 dark:hover:text-white z-10"
            title="Close"
          >
            <X size={28} />
          </button>

          {/* Left Sidebar - Settings */}
          <div className="w-80 bg-gray-50 dark:bg-gray-800 p-6 overflow-y-auto">
            <h2 className="text-xl font-bold mb-6">AI Image Upscaler</h2>

            {/* Step 1: Model Selection */}
            <div className="mb-6">
              <h3 className="text-sm font-semibold mb-3 text-gray-700 dark:text-gray-300">1. Select AI Model</h3>
              <select
                value={selectedModel}
                onChange={(e) => setSelectedModel(e.target.value)}
                className="w-full p-2 border rounded-md bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600"
              >
                {availableModels.map(model => (
                  <option key={model} value={model}>
                    {model === 'swinir-real-sr-x4' ? 'Upscayl Standard 4x (Recommended)' :
                     model === 'swinir-m-x4' ? 'Upscayl Lite 4x (Faster)' :
                     model === 'realesrgan-x4plus' ? 'Remacri 4x (Photo Enhancement)' :
                     model === 'realesrgan-x4plus-anime' ? 'Digital Art 4x (Anime/Art)' :
                     model === '4xlsdir' ? 'UltraSharp 4x (Sharp Details)' :
                     model === 'high-fidelity-4x' ? 'High Fidelity 4x (Premium Quality)' :
                     model === 'ultramix-balanced-4x' ? 'UltraMix Balanced 4x (All-Purpose)' :
                     model}
                  </option>
                ))}
              </select>
            </div>

            {/* Step 2: Scale Factor */}
            <div className="mb-6">
              <h3 className="text-sm font-semibold mb-3 text-gray-700 dark:text-gray-300">2. Scale Factor ({scaleFactor}x)</h3>
              <input
                type="range"
                min="2"
                max="8"
                value={scaleFactor}
                onChange={(e) => setScaleFactor(parseInt(e.target.value))}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>2x</span>
                <span>4x</span>
                <span>8x</span>
              </div>
            </div>
            {/* Step 3: Output Format */}
            <div className="mb-6">
              <h3 className="text-sm font-semibold mb-3 text-gray-700 dark:text-gray-300">3. Output Format</h3>
              <div className="flex gap-2">
                {['png', 'jpg', 'webp'].map(format => (
                  <button
                    key={format}
                    onClick={() => setImageFormat(format)}
                    className={`px-3 py-1 rounded text-sm ${
                      imageFormat === format
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300'
                    }`}
                  >
                    {format.toUpperCase()}
                  </button>
                ))}
              </div>
            </div>

            {/* Compression (for JPG/WEBP) */}
            {(imageFormat === 'jpg' || imageFormat === 'webp') && (
              <div className="mb-6">
                <h3 className="text-sm font-semibold mb-3 text-gray-700 dark:text-gray-300">Compression ({compression}%)</h3>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={compression}
                  onChange={(e) => setCompression(parseInt(e.target.value))}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>0% (Best Quality)</span>
                  <span>100% (Smallest Size)</span>
                </div>
              </div>
            )}

            {/* Advanced Settings Toggle */}
            <div className="mb-4">
              <button
                onClick={() => setShowAdvanced(!showAdvanced)}
                className="text-sm text-blue-500 hover:text-blue-600 dark:text-blue-400"
              >
                {showAdvanced ? '▼ Hide Advanced Settings' : '▶ Show Advanced Settings'}
              </button>
            </div>

            {/* Advanced Settings */}
            {showAdvanced && (
              <div className="space-y-4 mb-6 p-4 bg-gray-100 dark:bg-gray-700 rounded-lg">
                {/* Custom Width */}
                <div>
                  <label className="flex items-center text-sm">
                    <input
                      type="checkbox"
                      checked={useCustomWidth}
                      onChange={(e) => setUseCustomWidth(e.target.checked)}
                      className="mr-2"
                    />
                    Use Custom Width
                  </label>
                  {useCustomWidth && (
                    <input
                      type="number"
                      value={customWidth}
                      onChange={(e) => setCustomWidth(parseInt(e.target.value) || 0)}
                      placeholder="Width in pixels"
                      className="w-full mt-2 p-2 border rounded text-sm bg-white dark:bg-gray-600"
                    />
                  )}
                </div>

                {/* GPU ID */}
                <div>
                  <label className="block text-sm mb-1">GPU ID (optional)</label>
                  <input
                    type="text"
                    value={gpuId}
                    onChange={(e) => setGpuId(e.target.value)}
                    placeholder="0, 1, 2..."
                    className="w-full p-2 border rounded text-sm bg-white dark:bg-gray-600"
                  />
                </div>

                {/* Tile Size */}
                <div>
                  <label className="block text-sm mb-1">Tile Size ({tileSize}px)</label>
                  <input
                    type="range"
                    min="200"
                    max="800"
                    step="50"
                    value={tileSize}
                    onChange={(e) => setTileSize(parseInt(e.target.value))}
                    className="w-full"
                  />
                </div>

                {/* TTA Mode */}
                <div>
                  <label className="flex items-center text-sm">
                    <input
                      type="checkbox"
                      checked={ttaMode}
                      onChange={(e) => setTtaMode(e.target.checked)}
                      className="mr-2"
                    />
                    TTA Mode (Test Time Augmentation)
                  </label>
                  <p className="text-xs text-gray-500 mt-1">Slower but potentially better quality</p>
                </div>
              </div>
            )}

            {/* Upscale Button */}
            <button
              onClick={handleApply}
              disabled={isUpscaling || !imageUrl}
              className={`w-full py-3 px-4 rounded-lg font-semibold ${
                isUpscaling || !imageUrl
                  ? 'bg-gray-300 dark:bg-gray-600 text-gray-500 cursor-not-allowed'
                  : 'bg-blue-500 hover:bg-blue-600 text-white'
              }`}
            >
              {isUpscaling ? `Upscaling... ${progress}%` : 'Start Upscaling'}
            </button>

            {/* Error Display */}
            {error && (
              <div className="mt-4 p-3 bg-red-100 dark:bg-red-900 border border-red-300 dark:border-red-700 rounded-lg">
                <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
              </div>
            )}
          </div>

          {/* Right Panel - Image Preview */}
          <div className="flex-1 p-6 overflow-y-auto">
            {!showSlider && (
              <div className="flex flex-col items-center h-full">
                {imageUrl && (
                  <div className="mb-4">
                    <img src={imageUrl} alt={project.name} className="max-w-full max-h-[60vh] rounded-lg border shadow-lg" />
                    <p className="text-center text-sm text-gray-500 mt-2">Original Image</p>
                  </div>
                )}
                {isUpscaling && (
                  <div className="w-full max-w-md">
                    <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden mb-2">
                      <div
                        className="h-full bg-gradient-to-r from-blue-400 to-blue-600 transition-all"
                        style={{ width: `${progress}%` }}
                      />
                    </div>
                    <p className="text-center text-sm text-gray-600 dark:text-gray-400">
                      {progressMsg || `Processing with ${selectedModel}... ${progress}%`}
                    </p>
                  </div>
                )}
                {!imageUrl && !isUpscaling && (
                  <div className="flex items-center justify-center h-full text-gray-500">
                    <p>No image selected</p>
                  </div>
                )}
              </div>
            )}
            {/* Before/After Slider */}
            {showSlider && imageUrl && upscaledUrl && (
              <div className="flex flex-col items-center h-full">
                <h3 className="text-lg font-semibold mb-4">Before vs After Comparison</h3>
                <div className="relative w-full max-w-2xl h-[50vh] mb-6 select-none" ref={sliderRef}
                  onMouseDown={handleSliderDown}
                  onMouseMove={isDragging ? handleSliderMove : undefined}
                  onMouseUp={handleSliderUp}
                  onMouseLeave={handleSliderUp}
                  style={{ cursor: isDragging ? 'grabbing' : 'ew-resize' }}
                >
                  {/* Original image (left) */}
                  <img
                    src={imageUrl}
                    alt="Original"
                    className="absolute top-0 left-0 w-full h-full object-contain rounded-lg"
                    style={{ clipPath: `inset(0 ${100 - sliderX * 100}% 0 0)` }}
                    draggable={false}
                  />
                  {/* Upscaled image (right) */}
                  <img
                    src={upscaledUrl}
                    alt="Upscaled"
                    className="absolute top-0 left-0 w-full h-full object-contain rounded-lg"
                    style={{ clipPath: `inset(0 0 0 ${sliderX * 100}%)` }}
                    draggable={false}
                  />
                  {/* Slider handle with labels */}
                  <div
                    className="absolute top-0 left-0 h-full w-1.5 bg-blue-500 rounded-full shadow-lg z-10 flex flex-col items-center"
                    style={{ left: `calc(${sliderX * 100}% - 6px)` }}
                  >
                    <div className="absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2 w-6 h-6 bg-blue-600 border-2 border-white dark:border-gray-900 rounded-full shadow-lg flex items-center justify-center cursor-ew-resize">
                      <div className="w-2 h-2 bg-white rounded-full" />
                    </div>
                    {/* Labels */}
                    <span className="absolute -top-8 left-1/2 -translate-x-1/2 text-xs font-semibold text-blue-600 bg-white dark:bg-gray-900 px-2 py-0.5 rounded shadow">Compare</span>
                  </div>
                  {/* Side labels */}
                  <span className="absolute left-2 top-2 text-xs font-semibold bg-white dark:bg-gray-900 px-2 py-0.5 rounded shadow">← Original</span>
                  <span className="absolute right-2 top-2 text-xs font-semibold bg-white dark:bg-gray-900 px-2 py-0.5 rounded shadow">Upscaled ({scaleFactor}x) →</span>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3 justify-center">
                  <button
                    onClick={handleSave}
                    className="px-6 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg font-semibold flex items-center gap-2"
                  >
                    <Download size={18} />
                    Download Upscaled Image
                  </button>
                  <button
                    onClick={() => {
                      setShowSlider(false);
                      setUpscaledUrl(null);
                      setIsUpscaling(false);
                      setProgress(0);
                      setError(null);
                    }}
                    className="px-6 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg font-semibold"
                  >
                    Upscale Another
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Memoized GalleryGrid to prevent unnecessary re-renders
  const GalleryGrid = memo(({ projects, isDarkMode, thumbSize, ProjectCard }: {
    projects: Project[];
    isDarkMode: boolean;
    thumbSize: number;
    ProjectCard: React.FC<{ project: Project }>;
  }) => {
    // Calculate responsive breakpoints based on thumb size
    const gutter = 24;
    const containerWidth = window.innerWidth - 32; // Account for padding
    const maxColumns = Math.floor(containerWidth / (thumbSize + gutter));

    // Create breakpoint object for responsive masonry
    const breakpointCols = {
      default: Math.max(1, maxColumns),
      1400: Math.max(1, Math.floor(1400 / (thumbSize + gutter))),
      1200: Math.max(1, Math.floor(1200 / (thumbSize + gutter))),
      1000: Math.max(1, Math.floor(1000 / (thumbSize + gutter))),
      800: Math.max(1, Math.floor(800 / (thumbSize + gutter))),
      600: Math.max(1, Math.floor(600 / (thumbSize + gutter))),
      400: 1
    };

    return (
      <Masonry
        breakpointCols={breakpointCols}
        className="masonry-grid"
        columnClassName="masonry-grid_column"
      >
        {projects.map(project => (
          <ProjectCard key={project.id} project={project} />
        ))}
      </Masonry>
    );
  });

  return (
    <div className="flex flex-col h-full">
      {/* Header with controls */}
      <div className="flex flex-wrap items-center justify-between gap-3 mb-2 px-4">
        <div className="flex items-center gap-3">
          <label className={`text-xs font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Thumbnail Size</label>
          <input
            type="range"
            min={100}
            max={320}
            step={10}
            value={thumbSize}
            onChange={e => setThumbSize(Number(e.target.value))}
            className="w-32"
          />
          <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>{thumbSize}px</span>
        </div>
        {/* --- Search and Filter Controls --- */}
        <div className="flex items-center gap-2 flex-1 justify-center min-w-[260px] max-w-[480px]">
          <div className="relative flex-1">
            <input
              type="text"
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              placeholder="Search projects..."
              className={`w-full pl-9 pr-3 py-1.5 rounded-lg border ${isDarkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-400'} focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm`}
            />
            <Search className="absolute left-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
            {searchQuery && (
              <button
                className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
                onClick={() => setSearchQuery('')}
                tabIndex={-1}
                type="button"
              >
                <X className="w-4 h-4" />
              </button>
            )}
          </div>
          <select
            value={typeFilter}
            onChange={e => setTypeFilter(e.target.value as any)}
            className={`pl-2 pr-7 py-1.5 rounded-lg border text-sm font-medium ${isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'}`}
          >
            <option value="all">All Types</option>
            <option value="image-to-3d">Image to 3D</option>
            <option value="text-to-3d">Text to 3D</option>
            <option value="image-generation">Image Generation</option>
          </select>
        </div>
        {/* Upload buttons */}
        <div className="flex items-center gap-2">
          <button
            onClick={handleImageUpload}
            disabled={isUploading}
            className={`flex items-center gap-2 px-3 py-1.5 rounded-lg text-xs font-medium transition-colors ${
              isDarkMode
                ? 'bg-blue-700 hover:bg-blue-600 text-white disabled:bg-gray-600'
                : 'bg-blue-600 hover:bg-blue-700 text-white disabled:bg-gray-400'
            }`}
            title="Upload Image Project"
          >
            <ImageIcon className="w-3 h-3" />
            Upload Image
          </button>
          <button
            onClick={handleModelUpload}
            disabled={isUploading}
            className={`flex items-center gap-2 px-3 py-1.5 rounded-lg text-xs font-medium transition-colors ${
              isDarkMode
                ? 'bg-green-700 hover:bg-green-600 text-white disabled:bg-gray-600'
                : 'bg-green-600 hover:bg-green-700 text-white disabled:bg-gray-400'
            }`}
            title="Upload 3D Model Project"
          >
            <Box className="w-3 h-3" />
            Upload 3D
          </button>
        </div>
      </div>
      <div className="flex-1 overflow-y-auto px-4 py-2">
        <GalleryGrid
          projects={filteredProjects}
          isDarkMode={isDarkMode}
          thumbSize={thumbSize}
          ProjectCard={ProjectCard}
        />
      </div>

      {/* Project Viewer Modal */}
      {isViewerOpen && selectedProject && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2" onClick={handleCloseViewer}>
          <div className="bg-white dark:bg-gray-800 rounded-lg w-[98vw] h-[96vh] flex flex-col overflow-hidden" onClick={e => e.stopPropagation()}>
            <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center flex-shrink-0">
              <h2 className="text-xl font-bold text-gray-900 dark:text-white truncate mr-4">{selectedProject.name}</h2>
              <button onClick={handleCloseViewer} className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 flex-shrink-0">
                <X size={24} />
              </button>
            </div>
            <div className="flex-1 p-4 overflow-hidden">
              {(() => {
                try {
                  if (selectedProject.type === 'image-generation') {
                    return <ImageViewerContent project={selectedProject} />;
                  } else if (selectedProject.type === 'image-to-3d' || selectedProject.type === 'text-to-3d') {
                    return <ViewerContent project={selectedProject} />;
                  } else {
                    return <div className="text-center py-8">Unknown project type: {selectedProject.type}</div>;
                  }
                } catch (error) {
                  console.error('Error rendering project viewer:', error);
                  return <div className="text-center py-8 text-red-500">Error loading project details</div>;
                }
              })()}
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && projectToDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg p-6 max-w-md w-full max-h-[90vh] overflow-y-auto`}>
            <h3 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Delete Project
            </h3>
            <p className={`mb-6 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              Are you sure you want to delete "{projectToDelete.name}"? This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={handleDeleteCancel}
                disabled={isDeleting}
                className={`px-4 py-2 rounded-md ${
                  isDarkMode
                    ? 'bg-gray-700 hover:bg-gray-600 text-gray-300'
                    : 'bg-gray-200 hover:bg-gray-300 text-gray-800'
                } transition-colors duration-200 disabled:opacity-50`}
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteConfirm}
                disabled={isDeleting}
                className="px-4 py-2 rounded-md bg-red-600 hover:bg-red-700 text-white transition-colors duration-200 disabled:opacity-50"
              >
                {isDeleting ? 'Deleting...' : 'Delete Project'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Upload Project Modal */}
      {showUploadModal && uploadFile && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg p-6 max-w-md w-full max-h-[90vh] overflow-y-auto`}>
            <h3 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Upload {uploadType === 'image' ? 'Image' : '3D Model'} Project
            </h3>
            <div className="space-y-4">
              {/* File info */}
              <div className={`p-3 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                <div className="flex items-center gap-2 mb-2">
                  {uploadType === 'image' ? <ImageIcon className="w-4 h-4" /> : <Box className="w-4 h-4" />}
                  <span className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>{uploadFile.name}</span>
                </div>
                <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Size: {(uploadFile.size / 1024 / 1024).toFixed(2)} MB</div>
              </div>
              {/* Progress Bar */}
              {isUploading && (
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 overflow-hidden mb-2">
                  <div
                    className={`h-2.5 rounded-full transition-all duration-500 ease-out ${uploadProgress === null ? 'bg-blue-400 animate-pulse' : 'bg-blue-500'}`}
                    style={{ width: uploadProgress === null ? '100%' : `${uploadProgress}%` }}
                  ></div>
                </div>
              )}
              {/* Project name */}
              <div>
                <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Project Name *
                </label>
                <input
                  type="text"
                  value={projectName}
                  onChange={(e) => setProjectName(e.target.value)}
                  className={`w-full px-3 py-2 rounded-lg border ${
                    isDarkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                  placeholder="Enter project name"
                  autoFocus
                />
              </div>

              {/* Project description */}
              <div>
                <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Description (Optional)
                </label>
                <textarea
                  value={projectDescription}
                  onChange={(e) => setProjectDescription(e.target.value)}
                  className={`w-full px-3 py-2 rounded-lg border ${
                    isDarkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                  placeholder="Enter project description"
                  rows={3}
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={cancelUpload}
                className={`px-4 py-2 rounded-lg font-medium ${
                  isDarkMode
                    ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
                disabled={isUploading}
              >
                Cancel
              </button>
              <button
                onClick={handleUploadProject}
                disabled={!projectName.trim() || isUploading}
                className={`px-4 py-2 rounded-lg font-medium ${
                  !projectName.trim() || isUploading
                    ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                    : uploadType === 'image'
                      ? 'bg-blue-600 text-white hover:bg-blue-700'
                      : 'bg-green-600 text-white hover:bg-green-700'
                }`}
              >
                {isUploading ? 'Creating...' : 'Create Project'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Hidden file inputs */}
      <input
        ref={imageInputRef}
        type="file"
        accept="image/*"
        onChange={(e) => handleFileSelect(e, 'image')}
        style={{ display: 'none' }}
      />
      <input
        ref={modelInputRef}
        type="file"
        accept=".glb,.gltf,.ply,.obj"
        onChange={(e) => handleFileSelect(e, '3d')}
        style={{ display: 'none' }}
      />
      {/* Hidden ModelViewer for thumbnail capture */}
      {pendingModelThumb && pendingModelThumb.modelFilePath && pendingModelThumb.projectId && (
        <div style={{ position: 'absolute', left: -9999, top: -9999, width: 256, height: 256, pointerEvents: 'none', opacity: 0 }}>
          <ModelViewer
            ref={modelViewerRef}
            modelUrl={pendingModelThumb.modelFilePath}
            filePath={pendingModelThumb.modelFilePath}
            isTextured={true}
            isDarkMode={isDarkMode}
            onModelLoaded={() => {
              modelViewerRef.current && typeof modelViewerRef.current.getScreenshot === 'function'
                ? modelViewerRef.current.getScreenshot().then((screenshotUrl: string) => {
                    if (screenshotUrl) {
                      if (window.electronAPI.saveScreenshotToProject && typeof window.electronAPI.saveScreenshotToProject === 'function') {
                        window.electronAPI.saveScreenshotToProject(pendingModelThumb.projectId, screenshotUrl).then((result: any) => {
                          if (!result || !result.success) {
                            console.error('Failed to save thumbnail for 3D model:', result?.error);
                            alert('Failed to save thumbnail for 3D model.');
                          } else {
                            fetchProjects();
                          }
                          setPendingModelThumb(null);
                        }).catch((err: any) => {
                          console.error('Error saving screenshot to project:', err);
                          alert('Failed to save thumbnail for 3D model.');
                          setPendingModelThumb(null);
                        });
                      } else {
                        console.error('saveScreenshotToProject is not available on electronAPI');
                        alert('Failed to save thumbnail for 3D model.');
                        setPendingModelThumb(null);
                      }
                    } else {
                      console.error('ModelViewer did not return a screenshot URL.');
                      alert('Failed to capture thumbnail for 3D model.');
                      setPendingModelThumb(null);
                    }
                  }).catch((err: any) => {
                    console.error('Error getting screenshot from ModelViewer:', err);
                    setPendingModelThumb(null);
                  })
                : setPendingModelThumb(null);
            }}
          />
        </div>
      )}
    </div>
  );
});

export default ProjectsGallery;