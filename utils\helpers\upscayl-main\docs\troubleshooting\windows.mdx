---
title: Windows Troubleshooting
description: "Troubleshooting common issues with Upscayl on Windows"
---
- [Set the app to performance mode](https://youtube.com/watch?v=sxvs6qYHJmc) and make sure you have the right redistributables.
- Try [DirectX repair](http://blog.csdn.net/vbcom/article/details/6962388).
- [Disable switchable graphics](https://nvidia.custhelp.com/app/answers/detail/a_id/5182/~/unable-to-launch-vulkan-apps%2Fgame-on-notebooks-with-amd-radeon-igpus) if you can.
- [Enable hardware-accelerated GPU scheduling](https://www.howtogeek.com/756935/how-to-enable-hardware-accelerated-gpu-scheduling-in-windows-11) if you can.
- Run [VulkanCapsViewer](https://github.com/SaschaWillems/VulkanCapsViewer). See if your GPU supports Vulkan.
- Reinstall graphics drivers.

#### Credits
Thanks to @[JZeravik](https://github.com/JZeravik) for the switchable graphics tip.