{"name": "upscayl", "private": true, "version": "2.15.0", "productName": "Upscayl", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/NayamAmarshe"}, "homepage": "https://github.com/TGS963/upscayl", "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/NayamAmarshe"}, {"name": "TGS963", "email": "<EMAIL>", "url": "https://github.com/TGS963"}], "email": "<EMAIL>", "license": "AGPL-3.0", "description": "Upscayl - Free and Open Source AI Image Upscaler", "keywords": ["AI", "Upscaler", "Image Upscale", "Linux image upscale", "Topaz Gigapixel", "Linux", "KDE", "Gnome"], "main": "export/electron/index.js", "scripts": {"clean": "rimraf dist renderer/.next renderer/out", "start": "tsc && electron .", "dev": "tsc && electron .", "build": "tsc && npm run validate-schema && next build renderer", "tsc": "tsc", "pack-app": "tsc && npm run build && electron-builder --dir", "dist": "tsc && npm run build && cross-env DEBUG=* electron-builder", "dist:appimage": "tsc && npm run build && cross-env DEBUG=* electron-builder build -l AppImage", "dist:flatpak": "tsc && npm run build && cross-env DEBUG=* electron-builder build -l flatpak", "dist:deb": "tsc && npm run build && cross-env DEBUG=* electron-builder build -l deb", "dist:rpm": "tsc && npm run build && cross-env DEBUG=* electron-builder build -l rpm", "dist:zip": "tsc && npm run build && cross-env DEBUG=* electron-builder build -l zip", "dist:mac-zip": "tsc && npm run build && electron-builder build -m --universal", "dist:dmg": "tsc && npm run build && electron-builder build -m dmg", "dist:msi": "tsc && npm run build && cross-env DEBUG=* electron-builder build -w nsis", "dist:pkg": "tsc && npm run build && cross-env DEBUG=* electron-builder build -m pkg", "dist:mac": "tsc && npm run build && electron-builder --mac --universal", "dist:mac-dev": "tsc && npm run build && electron-builder --mac --arm64 -c mac-dev.json", "dist:mac-arm64": "tsc && npm run build && electron-builder --mac --arm64", "dist:mas": "npm run enable-store && tsc && npm run build && electron-builder --mac mas --universal -c mas.json && npm run disable-store", "dist:mas-dev": "npm run enable-store && tsc && npm run build && electron-builder --mac mas-dev --universal -c mas-dev.json && npm run disable-store", "dist:win": "tsc && npm run build && electron-builder --win", "dist:linux": "tsc && npm run build && electron-builder --linux", "publish-app": "tsc && npm run build && electron-builder -wlm --publish always", "publish-linux-app": "tsc && npm run build && electron-builder -l --publish always", "publish-win-app": "tsc && npm run build && electron-builder -w --publish always", "publish-mac-universal-app": "tsc && npm run build && electron-builder --mac --universal --publish always", "publish-mac-app": "tsc && npm run build && electron-builder --mac --x64 --publish always", "publish-mac-arm-app": "tsc && npm run build && electron-builder --mac --arm64 --publish always", "dist:appstore": "CSC_KEY_PASSWORD=$PASSWORD CSC_LINK=$(openssl base64 -in $CERTIFICATE_PATH) npm run dist:mac-mas", "enable-store": "sed -i '' -e 's/APP_STORE_BUILD: false,/APP_STORE_BUILD: true,/' common/feature-flags.ts", "disable-store": "sed -i '' -e 's/APP_STORE_BUILD: true,/APP_STORE_BUILD: false,/' common/feature-flags.ts", "validate-schema": "node scripts/validate-schema.js"}, "build": {"productName": "Upscayl", "appId": "org.upscayl.Upscayl", "artifactName": "${name}-${version}-${os}.${ext}", "afterSign": "./notarize.js", "asar": true, "asarUnpack": ["**/node_modules/sharp/**/*"], "extraFiles": [{"from": "resources/${os}/bin", "to": "resources/bin", "filter": ["**/*"]}, {"from": "resources/models", "to": "resources/models", "filter": ["**/*"]}, {"from": "resources/icons/128x128.png", "to": "resources/128x128.png"}, {"from": "resources/icons/512x512.png", "to": "resources/512x512.png"}], "mas": {"hardenedRuntime": false, "electronLanguages": ["en"], "category": "public.app-category.photography", "entitlements": "resources/entitlements.mas.plist", "entitlementsInherit": "resources/entitlements.mas.inherit.plist", "provisioningProfile": "embedded.provisionprofile", "mergeASARs": false, "gatekeeperAssess": false, "icon": "build/icon.icns", "x64ArchFiles": "*", "target": [{"target": "mas", "arch": ["universal"]}]}, "mac": {"hardenedRuntime": true, "gatekeeperAssess": false, "mergeASARs": false, "x64ArchFiles": "*", "minimumSystemVersion": "12.0.0", "category": "public.app-category.photography", "entitlements": "resources/entitlements.mac.plist", "entitlementsInherit": "resources/entitlements.mac.plist", "provisioningProfile": "embedded.provisionprofile", "type": null, "target": [{"target": "dmg", "arch": ["universal"]}, {"target": "zip", "arch": ["universal"]}]}, "dmg": {"sign": "false", "contents": [{"x": 130, "y": 220}, {"x": 410, "y": 220, "type": "link", "path": "/Applications"}]}, "linux": {"target": ["AppImage", "zip", "deb", "rpm"], "maintainer": "<PERSON><PERSON> <<EMAIL>>", "category": "Graphics;2DGraphics;RasterGraphics;ImageProcessing;", "synopsis": "AI Image Upscaler", "description": "Free and Open Source AI Image Upscaler", "icon": "resources/icons/"}, "win": {"publisherName": "Upscayl Team", "target": ["nsis", "zip"], "icon": "build/icon.png"}, "nsis": {"allowToChangeInstallationDirectory": true, "oneClick": false, "allowElevation": true, "perMachine": true}, "files": ["export", "renderer/out"], "publish": {"provider": "github"}}, "devDependencies": {"@electron/notarize": "^2.5.0", "@tailwindcss/typography": "^0.5.15", "@types/node": "^18.15.12", "@types/react": "^18.0.37", "@types/react-dom": "^18.0.11", "ajv": "^6.12.6", "autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "daisyui": "^4.12.23", "electron": "^33.2.1", "electron-builder": "^25.1.8", "next": "^15.2.4", "postcss": "^8.4.49", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.9", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwindcss": "^3.4.17", "typescript": "^4.8.4"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-toast": "^1.2.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "dotenv": "^16.4.7", "electron-is-dev": "^2.0.0", "electron-log": "^5.2.4", "electron-next": "^3.1.5", "electron-settings": "^4.0.4", "electron-updater": "^6.3.9", "eslint-config-next": "^14.2.7", "exiftool-vendored": "^29.3.0", "firebase": "^11.1.0", "gray-matter": "^4.0.3", "jotai": "^2.11.0", "lucide-react": "^0.469.0", "posthog-js": "^1.203.2", "react-compare-slider": "^3.1.0", "react-markdown": "^9.0.1", "react-resizable-panels": "^2.1.7", "react-select": "^5.9.0", "react-tooltip": "^5.28.0", "remark-gfm": "^4.0.0", "tailwind-merge": "^2.6.0", "tailwind-scrollbar": "^3.1.0", "tailwindcss-animate": "^1.0.7", "theme-change": "^2.5.0"}, "volta": {"node": "18.20.5"}}