const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const path = require('path');
const fs = require('fs');
const { spawn } = require('child_process');
const Store = require('electron-store');
const crypto = require('crypto');
const imageSize = require('image-size');
const { fork } = require('child_process');

const logger = require('./logger');
const { showSplashScreen, sendStatusToSplash } = require('./splashScreen');
const DependencyManager = require('./dependencyManager');
const PipelineManager = require('./pipelineManager');
const pipelineLoader = require('./pipelineLoader');

// Keep a global reference of the window object, if you don't, the window will
// be closed automatically when the JavaScript object is garbage collected.
let mainWindow = null;
const store = new Store();
const dependencyManager = new DependencyManager(store);
const pipelineManager = new PipelineManager(dependencyManager);

logger.info('Logger initialized.');

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 2200,
    height: 1200,
    minWidth: 1600,
    minHeight: 900,
    show: false, // Don't show the window immediately
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      devTools: true // Enable dev tools for debugging
    }
  });

  // Add error handling and logging
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
    logger.error(`Main window failed to load: ${errorCode} - ${errorDescription} for URL: ${validatedURL}`);
  });

  mainWindow.webContents.on('did-start-loading', () => {
    logger.info('Main window web contents started loading');
  });

  mainWindow.webContents.on('did-finish-load', () => {
    logger.info('Main window web contents finished loading');
  });

  mainWindow.webContents.on('dom-ready', () => {
    logger.info('Main window DOM ready');
  });

  // Force production mode to make sample gallery work
  const isDevelopment = false; // Always use production mode
  
  if (isDevelopment) {
    logger.info('Development mode detected - Loading Vite dev server: http://localhost:5173/');
    mainWindow.loadURL('http://localhost:5173/');
    // Only open dev tools in development mode
    mainWindow.webContents.openDevTools();
  } else {
    // Fixed path: production files are in dist/renderer, not src/renderer
    const indexPath = path.join(__dirname, '../../dist/renderer/index.html');
    logger.info(`Production mode - Loading file: ${indexPath}`);
    mainWindow.loadFile(indexPath);
    // Dev tools hidden by default in production, can be opened with F12
  }

  return mainWindow;
}

async function initializeApp() {
  try {
    // Create main window but don't show it yet
    const win = createWindow();
    
    // Show splash screen
    const splash = showSplashScreen(win);
    
    // Initialize dependencies
    sendStatusToSplash('Loading pipeline configurations...');
    // Note: Pipeline configs are now embedded - no template loading needed
    
    sendStatusToSplash('Scanning pipelines...');
    await dependencyManager.scanAndLoadPipelines();
    
    // Set the dependency manager's main window reference
    dependencyManager.setMainWindow(win);
    
    // Check only Core dependencies at startup (other pipelines checked on-demand)
    sendStatusToSplash('Checking core dependencies...');
    try {
      // Only check Core pipeline dependencies at startup
      await Promise.race([
        dependencyManager.checkDependencies('Core'),
        new Promise((_, reject) => setTimeout(() => reject(new Error('Core dependency check timeout')), 5000))
      ]);
      sendStatusToSplash('Core dependencies verified');
    } catch (error) {
      logger.warn('Core dependency check failed or timed out:', error.message);
      sendStatusToSplash('Core dependencies check skipped - continuing startup');
      // Continue startup even if core dependency check fails
    }
    
    // Final initialization steps
    sendStatusToSplash('Finalizing startup...');
    
    // Wait a bit for the main window to load, then show it
    setTimeout(() => {
      logger.info('Startup complete - showing main window');
      if (splash && !splash.isDestroyed()) {
        splash.close();
      }
      if (win && !win.isDestroyed()) {
        win.show();
      }
    }, 3000); // Give main window time to load
    
  } catch (error) {
    logger.error('Error during app initialization:', error);
    dialog.showErrorBox(
      'Initialization Error',
      'Failed to initialize the application. Please check the logs for details.'
    );
    app.quit();
  }
}

app.whenReady().then(async () => {
  logger.info('App is ready.');

  // Hide console window at startup (Windows only)
  if (process.platform === 'win32') {
    try {
      const { exec } = require('child_process');
      exec('powershell -Command "Add-Type -Name Window -Namespace Console -MemberDefinition \'[DllImport(\\\"Kernel32.dll\\\")] public static extern IntPtr GetConsoleWindow(); [DllImport(\\\"user32.dll\\\")] public static extern bool ShowWindow(IntPtr hWnd, Int32 nCmdShow);\'; $consolePtr = [Console.Window]::GetConsoleWindow(); [Console.Window]::ShowWindow($consolePtr, 0)"');
      logger.info('Console window hidden at startup');
    } catch (error) {
      logger.warn('Failed to hide console window at startup:', error.message);
    }
  }

  // Cleanup any lingering Python processes on startup
  try {
    const { cleanupPythonProcesses } = require('./trellisServer');
    await cleanupPythonProcesses();
    logger.info('Python process cleanup completed on startup');
  } catch (error) {
    logger.warn('Python process cleanup failed on startup:', error.message);
  }

  initializeApp();
  
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  logger.info('All windows closed.');
  if (process.platform !== 'darwin') {
    logger.info('Quitting app.');
    app.quit();
  }
});

// IPC Handlers

// General
ipcMain.handle('get-app-version', () => {
  logger.info('IPC: get-app-version called');
  return app.getVersion();
});

// Config
ipcMain.handle('get-config', (event, key) => {
  const store = new Store();
  logger.info(`IPC: get-config called for key: ${key}`);
  return store.get(key);
});

ipcMain.handle('set-config', (event, key, value) => {
  logger.info(`IPC: set-config called for key: ${key}`);
  try {
    // For the token, we just save it. Validation should be done before calling this.
    dependencyManager.setHuggingFaceToken(value);
    return { success: true, message: 'Token saved successfully.' };
  } catch (error) {
    logger.error(`Failed to set config for key ${key}:`, error);
    return { success: false, error: 'Failed to save settings.' };
  }
});

// Pipelines
ipcMain.handle('get-available-pipelines', async () => {
  logger.info('IPC: get-available-pipelines called');
  return pipelineLoader.getRegisteredPipelines();
});

ipcMain.handle('run-pipeline', async (event, name, data) => {
  logger.info(`IPC: run-pipeline called for: ${name}`);
  let ipcCallbackCounter = 0;
  return await pipelineLoader.runPipeline(name, data, (status) => {
    ipcCallbackCounter++;
    // Only log every 5th IPC status update to reduce spam, or important milestones
    if (ipcCallbackCounter % 5 === 0 || status.progress >= 100 || status.stage !== 'trellis') {
      console.log('[IPC Handler] Sending status:', `${status.stage} - ${status.progress}% - ${status.message}`);
    }
    event.sender.send('pipeline-status', status);
  });
});

// Projects
const PROJECTS_DIR = path.join(app.getAppPath(), 'projects');
const METADATA_DIR = path.join(PROJECTS_DIR, 'metadata');

ipcMain.handle('get-projects', () => {
  logger.info('IPC: get-projects called');
  if (!fs.existsSync(METADATA_DIR)) {
    logger.warn(`Metadata directory not found: ${METADATA_DIR}`);
    return [];
  }
  const projectFiles = fs.readdirSync(METADATA_DIR).filter(file => file.endsWith('.json'));

  const projects = projectFiles.map(file => {
    const filePath = path.join(METADATA_DIR, file);
    try {
      const projectData = JSON.parse(fs.readFileSync(filePath, 'utf-8'));
      const projectId = projectData.id;

      const createRelativePath = (fileType, fileName) => {
        if (!fileName) return undefined;
        let fullPath;
        if (fileType === 'thumbnail') {
          fullPath = path.join(PROJECTS_DIR, 'thumbnails', fileName);
        } else {
          fullPath = path.join(PROJECTS_DIR, 'files', projectId, fileName);
        }
        if (fs.existsSync(fullPath)) {
          return path.relative(app.getAppPath(), fullPath).replace(/\\/g, '/');
        }
        logger.warn(`[get-projects] File not found for project ${projectId}: ${fullPath}`);
        return undefined;
      };

      projectData.thumbnail_url = createRelativePath('thumbnail', projectData.files?.thumbnail);
      if (projectData.files?.model) {
        projectData.files.model = createRelativePath('model', projectData.files.model);
      }
      if (projectData.files?.video) {
        projectData.files.video = createRelativePath('video', projectData.files.video);
      }
      if (projectData.files?.generated_image) {
        projectData.files.generated_image = createRelativePath('generated_image', projectData.files.generated_image);
      }
      if (projectData.original_image_path) {
        projectData.original_image_path = createRelativePath('original_image', path.basename(projectData.original_image_path));
      }
      
      return projectData;
    } catch (error) {
      logger.error(`Error reading project file ${file}:`, error);
      return null;
    }
  }).filter(p => p !== null);

  return projects.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
});

ipcMain.handle('get-project-details', (event, id) => {
  logger.info(`IPC: get-project-details called for id: ${id}`);
  const projectPath = path.join(METADATA_DIR, `${id}.json`);

  if (fs.existsSync(projectPath)) {
    try {
      const projectData = JSON.parse(fs.readFileSync(projectPath, 'utf-8'));
      const projectId = projectData.id;

      const createRelativePath = (fileType, fileName) => {
        if (!fileName) return undefined;
        let fullPath;
        if (fileType === 'thumbnail') {
          fullPath = path.join(PROJECTS_DIR, 'thumbnails', fileName);
        } else if (fileType === 'original_image') {
          // original_image_path is already a full path, we need to find it in uploads
          const uploadsDir = path.join(app.getAppPath(), 'uploads');
          fullPath = path.join(uploadsDir, fileName);
        }
        else {
          fullPath = path.join(PROJECTS_DIR, 'files', projectId, fileName);
        }
        if (fs.existsSync(fullPath)) {
          return path.relative(app.getAppPath(), fullPath).replace(/\\/g, '/');
        }
        logger.warn(`[get-project-details] File not found for project ${projectId}: ${fullPath}`);
        return undefined;
      };

      projectData.thumbnail_url = createRelativePath('thumbnail', projectData.files?.thumbnail);
      if (projectData.files?.model) {
        projectData.files.model = createRelativePath('model', projectData.files.model);
      }
      if (projectData.files?.generated_image) {
        projectData.files.generated_image = createRelativePath('generated_image', projectData.files.generated_image);
      }
      if (projectData.original_image_path) {
        projectData.original_image_path = createRelativePath('original_image', path.basename(projectData.original_image_path));
      }
      
      return projectData;
    } catch (error) {
      logger.error(`Error reading project.json for ${id}:`, error);
    }
  }
  return null;
});

ipcMain.handle('update-project', (event, id, data) => {
  logger.info(`IPC: update-project called for id: ${id}`);
  const projectPath = path.join(METADATA_DIR, `${id}.json`);
  if (fs.existsSync(projectPath)) {
    try {
      let projectData = JSON.parse(fs.readFileSync(projectPath, 'utf-8'));
      projectData = { ...projectData, ...data };
      fs.writeFileSync(projectPath, JSON.stringify(projectData, null, 2));
      return { success: true, project: projectData };
    } catch (error) {
      logger.error(`Error updating project ${id}:`, error);
      return { success: false, error: 'Failed to update project data.' };
    }
  }
  return { success: false, error: 'Project not found.' };
});

ipcMain.handle('delete-project', (event, id) => {
  logger.info(`IPC: delete-project called for id: ${id}`);
  try {
    const metadataPath = path.join(METADATA_DIR, `${id}.json`);
    const filesDir = path.join(PROJECTS_DIR, 'files', id);
    let thumbnailPath;

    if (fs.existsSync(metadataPath)) {
      const projectData = JSON.parse(fs.readFileSync(metadataPath, 'utf-8'));
      if (projectData.files?.thumbnail) {
        thumbnailPath = path.join(PROJECTS_DIR, 'thumbnails', projectData.files.thumbnail);
      }
      fs.unlinkSync(metadataPath);
    }

    if (thumbnailPath && fs.existsSync(thumbnailPath)) {
      fs.unlinkSync(thumbnailPath);
    }

    if (fs.existsSync(filesDir)) {
      fs.rmSync(filesDir, { recursive: true, force: true });
    }
    
    return { success: true };
  } catch (error) {
    logger.error(`Error deleting project ${id}:`, error);
    return { success: false, error: 'Failed to delete project.' };
  }
});

// Sample Images
const SAMPLES_DIR = path.join(app.getAppPath(), 'sample_images');
const SAMPLES_JSON_PATH = path.join(SAMPLES_DIR, 'samples.json');

// Image Gallery Storage
const GALLERY_DIR = path.join(app.getPath('userData'), 'image_gallery');
const COLLECTIONS_JSON_PATH = path.join(GALLERY_DIR, 'collections.json');

// Ensure gallery directory exists
if (!fs.existsSync(GALLERY_DIR)) {
  fs.mkdirSync(GALLERY_DIR, { recursive: true });
}

function readSamples() {
  if (fs.existsSync(SAMPLES_JSON_PATH)) {
    return JSON.parse(fs.readFileSync(SAMPLES_JSON_PATH, 'utf-8'));
  }
  return [];
}

function writeSamples(samples) {
  fs.writeFileSync(SAMPLES_JSON_PATH, JSON.stringify(samples, null, 2));
}

// Image Gallery Collection Management
function readCollections() {
  if (fs.existsSync(COLLECTIONS_JSON_PATH)) {
    try {
      return JSON.parse(fs.readFileSync(COLLECTIONS_JSON_PATH, 'utf-8'));
    } catch (error) {
      logger.error('Error reading collections:', error);
      return [{ name: 'Default', images: [] }];
    }
  }
  return [{ name: 'Default', images: [] }];
}

function writeCollections(collections) {
  try {
    fs.writeFileSync(COLLECTIONS_JSON_PATH, JSON.stringify(collections, null, 2));
  } catch (error) {
    logger.error('Error writing collections:', error);
  }
}

function saveImageToGallery(base64Data, filename = null) {
  try {
    // Generate unique filename if not provided
    if (!filename) {
      const timestamp = Date.now();
      const randomId = Math.random().toString(36).substring(2, 8);
      filename = `generated_${timestamp}_${randomId}.png`;
    }

    const imagePath = path.join(GALLERY_DIR, filename);

    // Remove data URL prefix if present
    const base64String = base64Data.replace(/^data:image\/[a-z]+;base64,/, '');

    // Save image to disk
    fs.writeFileSync(imagePath, base64String, 'base64');

    logger.info(`Image saved to gallery: ${filename}`);
    return filename;
  } catch (error) {
    logger.error('Error saving image to gallery:', error);
    return null;
  }
}

function loadImageFromGallery(filename) {
  try {
    const imagePath = path.join(GALLERY_DIR, filename);
    if (fs.existsSync(imagePath)) {
      const fileBuffer = fs.readFileSync(imagePath);
      const mimeType = path.extname(filename).toLowerCase() === '.png' ? 'image/png' : 'image/jpeg';
      return `data:${mimeType};base64,${fileBuffer.toString('base64')}`;
    }
    return null;
  } catch (error) {
    logger.error(`Error loading image from gallery: ${filename}`, error);
    return null;
  }
}

function deleteImageFromGallery(filename) {
  try {
    const imagePath = path.join(GALLERY_DIR, filename);
    if (fs.existsSync(imagePath)) {
      fs.unlinkSync(imagePath);
      logger.info(`Image deleted from gallery: ${filename}`);
      return true;
    }
    return false;
  } catch (error) {
    logger.error(`Error deleting image from gallery: ${filename}`, error);
    return false;
  }
}

ipcMain.handle('get-sample-images', () => {
  logger.info('IPC: get-sample-images called');
  
  try {
    const samples = readSamples();
    logger.info(`Loading ${samples.length} sample images`);
    
    return samples.map((sample, index) => {
      try {
        if (sample.filename) {
          const imagePath = path.join(SAMPLES_DIR, sample.filename);
          if (fs.existsSync(imagePath)) {
            const fileBuffer = fs.readFileSync(imagePath);
            const mimeType = path.extname(sample.filename).toLowerCase() === '.png' ? 'image/png' : 'image/jpeg';
            sample.url = `data:${mimeType};base64,${fileBuffer.toString('base64')}`;
            logger.info(`Loaded sample ${index + 1}/${samples.length}: ${sample.name}`);
          } else {
            logger.warn(`Sample image file not found: ${imagePath}`);
            sample.url = null;
            sample.error = 'File not found';
          }
        }
        return sample;
      } catch (error) {
        logger.error(`Error loading sample image ${sample.filename}:`, error);
        sample.url = null;
        sample.error = error.message;
        return sample;
      }
    });
  } catch (error) {
    logger.error('Error reading samples.json:', error);
    return [];
  }
});

ipcMain.handle('update-sample-image', (event, filename, data) => {
  logger.info(`IPC: update-sample-image called for: ${filename}`);
  const samples = readSamples();
  const sampleIndex = samples.findIndex(s => s.filename === filename);

  if (sampleIndex === -1) {
    logger.error(`Sample image not found: ${filename}`);
    return null;
  }

  const updatedSample = {
    ...samples[sampleIndex],
    name: data.name,
    category: data.category,
    description: data.name, // Also update description for now
  };

  samples[sampleIndex] = updatedSample;
  writeSamples(samples);

  // Add the URL back for the renderer
  const imagePath = path.join(SAMPLES_DIR, updatedSample.filename);
  if (fs.existsSync(imagePath)) {
    const fileBuffer = fs.readFileSync(imagePath);
    const mimeType = path.extname(updatedSample.filename) === '.png' ? 'image/png' : 'image/jpeg';
    updatedSample.url = `data:${mimeType};base64,${fileBuffer.toString('base64')}`;
  }

  return updatedSample;
});

ipcMain.handle('upload-sample-image', (event, data) => {
  logger.info(`IPC: upload-sample-image called for: ${data.filename}`);
  
  try {
    // Create a unique filename with timestamp to avoid conflicts
    const timestamp = Date.now();
    const fileExtension = path.extname(data.filename);
    const baseName = path.basename(data.filename, fileExtension);
    const filename = `${timestamp}_${baseName}${fileExtension}`;
    
    // Save the file to sample_images directory
    const imagePath = path.join(SAMPLES_DIR, filename);
    fs.writeFileSync(imagePath, Buffer.from(data.buffer));
    
    // Read existing samples
    const samples = readSamples();
    
    // Create new sample entry
    const newSample = {
      id: `${timestamp}-${samples.length}`,
      name: baseName.replace(/[_-]/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      filename: filename,
      category: data.category,
      description: `Sample image: ${baseName}`,
    };
    
    // Add to samples array
    samples.push(newSample);
    writeSamples(samples);
    
    // Add URL for return
    const fileBuffer = fs.readFileSync(imagePath);
    const mimeType = fileExtension.toLowerCase() === '.png' ? 'image/png' : 'image/jpeg';
    newSample.url = `data:${mimeType};base64,${fileBuffer.toString('base64')}`;
    
    logger.info(`Successfully uploaded sample image: ${filename}`);
    return newSample;
  } catch (error) {
    logger.error(`Error uploading sample image:`, error);
    return null;
  }
});

ipcMain.handle('delete-sample-image', (event, filename) => {
  logger.info(`IPC: delete-sample-image called for: ${filename}`);
  
  try {
    // Read existing samples
    const samples = readSamples();
    
    // Remove sample from array
    const filteredSamples = samples.filter(sample => sample.filename !== filename);
    
    if (filteredSamples.length === samples.length) {
      logger.warn(`Sample image not found in samples.json: ${filename}`);
    } else {
      writeSamples(filteredSamples);
      logger.info(`Removed sample from samples.json: ${filename}`);
    }
    
    // Delete the physical file
    const imagePath = path.join(SAMPLES_DIR, filename);
    if (fs.existsSync(imagePath)) {
      fs.unlinkSync(imagePath);
      logger.info(`Deleted sample image file: ${imagePath}`);
    } else {
      logger.warn(`Sample image file not found: ${imagePath}`);
    }
    
    return;
  } catch (error) {
    logger.error(`Error deleting sample image:`, error);
    throw error;
  }
});

// Image Gallery IPC Handlers
ipcMain.handle('get-image-collections', () => {
  logger.info('IPC: get-image-collections called');
  try {
    const collections = readCollections();

    // Load image data for each collection
    const collectionsWithImages = collections.map(collection => ({
      ...collection,
      images: collection.images.map(filename => {
        const imageData = loadImageFromGallery(filename);
        return imageData ? { filename, data: imageData } : null;
      }).filter(Boolean)
    }));

    logger.info(`Loaded ${collectionsWithImages.length} collections`);
    return collectionsWithImages;
  } catch (error) {
    logger.error('Error loading image collections:', error);
    return [{ name: 'Default', images: [] }];
  }
});

ipcMain.handle('save-image-collections', (event, collections) => {
  logger.info('IPC: save-image-collections called');
  try {
    // Convert collections to file-based format (store filenames instead of base64 data)
    const fileBasedCollections = collections.map(collection => ({
      name: collection.name,
      images: collection.images.map(img => {
        if (typeof img === 'string') {
          // If it's already a filename, keep it
          if (!img.startsWith('data:')) {
            return img;
          }
          // If it's base64 data, save it and return filename
          return saveImageToGallery(img);
        } else if (img && img.filename) {
          // If it's an object with filename, return the filename
          return img.filename;
        }
        return null;
      }).filter(Boolean)
    }));

    writeCollections(fileBasedCollections);
    logger.info('Image collections saved successfully');
    return { success: true };
  } catch (error) {
    logger.error('Error saving image collections:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('add-image-to-gallery', (event, { base64Data, collectionName }) => {
  logger.info(`IPC: add-image-to-gallery called for collection: ${collectionName}`);
  try {
    // Save image to disk
    const filename = saveImageToGallery(base64Data);
    if (!filename) {
      throw new Error('Failed to save image to disk');
    }

    // Update collections
    const collections = readCollections();
    const updatedCollections = collections.map(collection => {
      if (collection.name === collectionName) {
        return {
          ...collection,
          images: [filename, ...collection.images]
        };
      }
      return collection;
    });

    writeCollections(updatedCollections);

    // Return the image data for immediate display
    const imageData = loadImageFromGallery(filename);
    logger.info(`Image added to gallery: ${filename}`);
    return { success: true, filename, data: imageData };
  } catch (error) {
    logger.error('Error adding image to gallery:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('delete-image-from-gallery', (event, { filename, collectionName }) => {
  logger.info(`IPC: delete-image-from-gallery called: ${filename} from ${collectionName}`);
  try {
    // Remove from collections
    const collections = readCollections();
    const updatedCollections = collections.map(collection => {
      if (collection.name === collectionName) {
        return {
          ...collection,
          images: collection.images.filter(img => img !== filename)
        };
      }
      return collection;
    });

    writeCollections(updatedCollections);

    // Delete file from disk
    deleteImageFromGallery(filename);

    logger.info(`Image deleted from gallery: ${filename}`);
    return { success: true };
  } catch (error) {
    logger.error('Error deleting image from gallery:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('download-file', async (event, relativePath) => {
  logger.info(`IPC: download-file called for: ${relativePath}`);
  const sourcePath = path.join(app.getAppPath(), relativePath);
  const { filePath } = await dialog.showSaveDialog(BrowserWindow.fromWebContents(event.sender), {
    defaultPath: path.basename(sourcePath)
  });
  if (filePath) {
    fs.copyFileSync(sourcePath, filePath);
  }
});

// Logs
const LOGS_DIR = path.join(app.getAppPath(), 'logs');

ipcMain.handle('get-logs', () => {
  logger.info('IPC: get-logs called');
  try {
    const logFile = path.join(LOGS_DIR, 'main.log');
    if (fs.existsSync(logFile)) {
      return fs.readFileSync(logFile, 'utf-8');
    }
  } catch (error) {
    logger.error('Error reading log file:', error);
  }
  return '';
});

ipcMain.handle('clear-logs', () => {
  logger.info('IPC: clear-logs called');
  try {
    const logFile = path.join(LOGS_DIR, 'main.log');
    const errorFile = path.join(LOGS_DIR, 'error.log');
    if (fs.existsSync(logFile)) {
      fs.writeFileSync(logFile, '');
    }
    if (fs.existsSync(errorFile)) {
        fs.writeFileSync(errorFile, '');
    }
  } catch (error) {
    logger.error('Error clearing log files:', error);
  }
});

// Command Window Toggle
let isConsoleVisible = false;

ipcMain.handle('toggle-console', () => {
  logger.info('IPC: toggle-console called');
  try {
    if (process.platform === 'win32') {
      const { exec } = require('child_process');

      if (isConsoleVisible) {
        // Hide console window
        exec('powershell -Command "Add-Type -Name Window -Namespace Console -MemberDefinition \'[DllImport(\\\"Kernel32.dll\\\")] public static extern IntPtr GetConsoleWindow(); [DllImport(\\\"user32.dll\\\")] public static extern bool ShowWindow(IntPtr hWnd, Int32 nCmdShow);\'; $consolePtr = [Console.Window]::GetConsoleWindow(); [Console.Window]::ShowWindow($consolePtr, 0)"');
        isConsoleVisible = false;
        logger.info('Console window hidden');
      } else {
        // Show console window, center it, and bring to front
        const powershellScript = `
          Add-Type -Name Window -Namespace Console -MemberDefinition '
            [DllImport("Kernel32.dll")] public static extern IntPtr GetConsoleWindow();
            [DllImport("user32.dll")] public static extern bool ShowWindow(IntPtr hWnd, Int32 nCmdShow);
            [DllImport("user32.dll")] public static extern bool SetForegroundWindow(IntPtr hWnd);
            [DllImport("user32.dll")] public static extern bool SetWindowPos(IntPtr hWnd, IntPtr hWndInsertAfter, int X, int Y, int cx, int cy, uint uFlags);
            [DllImport("user32.dll")] public static extern bool GetWindowRect(IntPtr hWnd, out RECT lpRect);
            [DllImport("user32.dll")] public static extern int GetSystemMetrics(int nIndex);
            public struct RECT { public int Left; public int Top; public int Right; public int Bottom; }
          ';

          $consolePtr = [Console.Window]::GetConsoleWindow();
          if ($consolePtr -ne [IntPtr]::Zero) {
            # Show the window first
            [Console.Window]::ShowWindow($consolePtr, 1);

            # Get screen dimensions
            $screenWidth = [Console.Window]::GetSystemMetrics(0);  # SM_CXSCREEN
            $screenHeight = [Console.Window]::GetSystemMetrics(1); # SM_CYSCREEN

            # Set window size and center it
            $windowWidth = 800;
            $windowHeight = 600;
            $x = ($screenWidth - $windowWidth) / 2;
            $y = ($screenHeight - $windowHeight) / 2;

            # Position and resize window (SWP_NOZORDER = 0x0004, SWP_SHOWWINDOW = 0x0040)
            [Console.Window]::SetWindowPos($consolePtr, [IntPtr]::Zero, $x, $y, $windowWidth, $windowHeight, 0x0044);

            # Bring to foreground
            [Console.Window]::SetForegroundWindow($consolePtr);
          }
        `;

        exec(`powershell -Command "${powershellScript.replace(/"/g, '\\"')}"`);
        isConsoleVisible = true;
        logger.info('Console window shown, centered, and brought to front');
      }

      return { success: true, visible: isConsoleVisible };
    } else {
      logger.warn('Console toggle not supported on this platform');
      return { success: false, error: 'Console toggle only supported on Windows' };
    }
  } catch (error) {
    logger.error('Error toggling console:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-console-visibility', () => {
  logger.info('IPC: get-console-visibility called');
  return { visible: isConsoleVisible };
});

// Dependency Manager
ipcMain.handle('get-dependency-status', async () => {
  logger.info('IPC: get-dependency-status called');
  try {
    const status = await dependencyManager.getDependencyStatus();
    return { success: true, data: status };
  } catch (error) {
    logger.error('Failed to get dependency status:', error);
    return { success: false, error: error.message };
  }
});

// File Loading
ipcMain.handle('load-file', (event, relativePath) => {
  if (!relativePath || typeof relativePath !== 'string') {
    logger.warn(`[load-file] Received invalid request for: ${relativePath}`);
    return null;
  }
  logger.info(`[load-file] Received request for: ${relativePath}`);
  let absolutePath = relativePath;
  if (!path.isAbsolute(relativePath)) {
    absolutePath = path.join(app.getAppPath(), relativePath);
  }
  logger.info(`[load-file] Reading absolute path: ${absolutePath}`);
  try {
    const data = fs.readFileSync(absolutePath);
    
    let mimeType = 'application/octet-stream'; // Default
    const extension = path.extname(relativePath).toLowerCase();
    if (extension === '.glb') {
      mimeType = 'model/gltf-binary';
    } else if (extension === '.ply') {
      mimeType = 'application/ply'; // A common, though not official, MIME type for PLY
    }

    return `data:${mimeType};base64,${data.toString('base64')}`;
  } catch (error) {
    logger.error(`[load-file] Failed to load file: ${absolutePath}`, error);
    return null;
  }
});

ipcMain.handle('validate-hf-token', async (event, token) => {
  logger.info('IPC: validate-hf-token called.');
  return new Promise((resolve) => {
    const pythonExe = dependencyManager._getPythonExe('Core');
    const scriptPath = path.join(__dirname, 'python_helpers', 'validate_hf_token.py');
    const args = [scriptPath, token];

    const validator = spawn(pythonExe, args);
    let stdout = '';
    let stderr = '';

    validator.stdout.on('data', (data) => {
      stdout += data.toString();
      logger.info(`[HF Token Validator]: ${stdout}`);
    });
    validator.stderr.on('data', (data) => {
      stderr += data.toString();
      logger.error(`[HF Token Validator]: ${stderr}`);
    });

    validator.on('close', (code) => {
      if (code === 0) {
        resolve({ success: true, message: stdout });
      } else {
        resolve({ success: false, error: stderr || stdout });
      }
    });
  });
});

// Handle file uploads
ipcMain.handle('upload-file', async (event, { buffer, filename }) => {
  // === Debug information about the incoming payload ===
  let sizeInfo = 'unknown';
  try {
    if (Buffer.isBuffer(buffer)) sizeInfo = `${buffer.length} bytes (Buffer)`;
    else if (buffer instanceof Uint8Array) sizeInfo = `${buffer.byteLength} bytes (Uint8Array)`;
    else if (buffer instanceof ArrayBuffer) sizeInfo = `${buffer.byteLength} bytes (ArrayBuffer)`;
    else if (buffer && buffer.type === 'Buffer' && Array.isArray(buffer.data)) sizeInfo = `${buffer.data.length} bytes (Serialized Buffer)`;
  } catch {}

  logger.info(`[upload-file] Received – filename: ${filename || 'N/A'}, size: ${sizeInfo}`);

  const imageId = crypto.randomUUID();
  const uploadsDir = path.join(app.getAppPath(), 'uploads');
  const uploadSubDir = path.join(uploadsDir, imageId);
  
  try {
    // 1. Save original file
    fs.mkdirSync(uploadSubDir, { recursive: true });
    const safeName = path.basename(filename);
    const originalFilePath = path.join(uploadSubDir, safeName);

    // Reconstruct buffer regardless of how it was serialized over IPC
    let dataToWrite;
    try {
      if (Buffer.isBuffer(buffer)) {
        dataToWrite = buffer;
      } else if (buffer instanceof Uint8Array || buffer instanceof ArrayBuffer) {
        dataToWrite = Buffer.from(buffer);
      } else if (buffer && buffer.type === 'Buffer' && Array.isArray(buffer.data)) {
        dataToWrite = Buffer.from(buffer.data);
      } else {
        throw new Error('Unrecognized buffer format received from renderer');
      }
    } catch (convErr) {
      logger.error(`Failed to reconstruct buffer: ${convErr.message}`);
      throw convErr;
    }

    logger.info(`Buffer received (bytes): ${dataToWrite.length}`);

    fs.writeFileSync(originalFilePath, dataToWrite);
    logger.info(`Uploaded original file saved: ${originalFilePath}`);

    // 2. Run background removal script
    const processedFileName = `${path.parse(safeName).name}_processed.png`;
    const processedFilePath = path.join(uploadSubDir, processedFileName);
    
    let bgRemovalSucceeded = false;
    try {
      logger.info(`Starting background removal for ${originalFilePath}...`);
      await new Promise((resolve, reject) => {
        const pythonExe = dependencyManager._getPythonExe('Core');
        const scriptPath = path.join(__dirname, 'python_helpers', 'remove_background.py');
        const args = [scriptPath, originalFilePath, processedFilePath];

        const venvDir = path.join(app.getAppPath(), 'pipelines', 'Core', 'venv');
        const scriptsDir = path.dirname(pythonExe);
        const env = { ...process.env };
        delete env.PYTHONHOME;
        delete env.PYTHONPATH;
        env.VIRTUAL_ENV = venvDir;
        env.PATH = `${scriptsDir}${path.delimiter}${env.PATH}`;

        logger.info(`Spawning: ${pythonExe} ${args.join(' ')}`);

        const remover = spawn(pythonExe, args, {
          env,
          windowsHide: true
        });
        let stderr = '';

        remover.stderr.on('data', (data) => {
          stderr += data.toString();
          logger.error(`[BG Remover]: ${data.toString()}`);
        });

        remover.on('close', (code) => {
          if (code === 0) {
            bgRemovalSucceeded = true;
            resolve();
          } else {
            reject(new Error(`Background remover exited with code ${code}: ${stderr}`));
          }
        });
        remover.on('error', (err) => reject(err));
      });
    } catch (bgErr) {
      logger.warn(`Background removal failed: ${bgErr.message}. Falling back to original image.`);
      // Fallback: just copy the original image to processedFilePath as PNG
      try {
        fs.copyFileSync(originalFilePath, processedFilePath);
      } catch (copyErr) {
        logger.error(`Failed to copy original image as fallback: ${copyErr.message}`);
      }
    }

    // 3. Return info about the processed (or fallback) file
    return {
      success: true,
      bg_removed: bgRemovalSucceeded,
      image_id: imageId,
      filename: processedFileName,
      path: path.relative(app.getAppPath(), processedFilePath).replace(/\\/g, '/'),
    };

  } catch (error) {
    logger.error('Error handling file upload and processing:', error);
    // Cleanup the directory if any step failed
    if (fs.existsSync(uploadSubDir)) {
      fs.rm(uploadSubDir, { recursive: true, force: true }, (err) => {
        if (err) logger.error(`Failed to cleanup upload directory ${uploadSubDir}:`, err);
      });
    }
    return { success: false, error: error.message };
  }
});

// IPC handlers for pipeline operations
ipcMain.handle('generate-trellis-3d', async (event, { imagePath, outputPath, settings }) => {
  try {
    return await pipelineManager.generateTrellis3D(imagePath, outputPath, settings);
  } catch (error) {
    logger.error('Failed to generate 3D model:', error);
    return {
      success: false,
      error: error.message
    };
  }
});

// IPC handler for Hunyaun 3D generation
ipcMain.handle('generate-hunyaun-3d', async (event, { imagePath, outputPath, settings }) => {
  try {
    return await pipelineManager.generateHunyaun3D(imagePath, outputPath, settings);
  } catch (error) {
    logger.error('Failed to generate Hunyaun 3D model:', error);
    return {
      success: false,
      error: error.message
    };
  }
});

// IPC handler for image generation
ipcMain.handle('generate-image', async (event, { prompt, model, settings, outputPath }) => {
  try {
    logger.info(`Generating image with model: ${model}, prompt: ${prompt}`);
    
    // Map UI model names to pipeline model names
    const modelMapping = {
      'flux': 'flux-schnell',
      'flux-schnell': 'flux-schnell',
      'flux-dev': 'flux-dev',
      'sdxl_turbo': 'sdxl-turbo',
      'sdxl-turbo': 'sdxl-turbo',
      'sdxl': 'stable-diffusion-xl-base-1.0',
      'sdxl_base': 'stable-diffusion-xl-base-1.0',
      'stable-diffusion-xl-base-1.0': 'stable-diffusion-xl-base-1.0',
      'sdxl_refiner': 'stable-diffusion-xl-refiner-1.0',
      'stable-diffusion-xl-refiner-1.0': 'stable-diffusion-xl-refiner-1.0',
      'stable_diffusion': 'stable-diffusion-v1-5',
      'sd_v1_5': 'stable-diffusion-v1-5',
      'stable-diffusion-v1-5': 'stable-diffusion-v1-5',
      'sd_v2_1': 'stable-diffusion-2-1',
      'stable-diffusion-2-1': 'stable-diffusion-2-1'
    };
    
    const pipelineModel = modelMapping[model] || model;
    
    // Create full output path
    const timestamp = Date.now();
    const imageId = crypto.randomUUID();
    const outputDir = path.join(app.getAppPath(), 'output');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    const fullOutputPath = outputPath || path.join(outputDir, `generated_${timestamp}_${imageId}.png`);
    
    // Prepare settings for pipeline
    const pipelineSettings = {
      model: pipelineModel,
      ...settings
    };
    
    const result = await pipelineManager.generateImage(prompt, fullOutputPath, pipelineSettings);
    
    if (result.success) {
      // Read the generated image as base64
      try {
        if (!fs.existsSync(result.output_path)) {
          throw new Error(`Generated image file not found: ${result.output_path}`);
        }
        const imageData = fs.readFileSync(result.output_path);
        const imageBase64 = imageData.toString('base64');
        
        return {
          success: true,
          image_base64: imageBase64,
          output_path: result.output_path,
          model: pipelineModel,
          prompt: prompt,
          settings: pipelineSettings
        };
      } catch (fileError) {
        throw new Error(`Failed to read generated image: ${fileError.message}`);
      }
    } else {
      throw new Error(result.error || 'Image generation failed');
    }
    
  } catch (error) {
    logger.error('Failed to generate image:', error);
    return {
      success: false,
      error: error.message
    };
  }
});

// IPC handler for streaming image generation (for progress updates)
ipcMain.handle('generate-image-stream', async (event, { prompt, model, settings, outputPath }) => {
  try {
    logger.info(`Starting streaming image generation with model: ${model}`);
    
    // Send initial progress
    event.sender.send('image-generation-progress', {
      progress: 0,
      status: 'Initializing image generation...'
    });
    
    // Map UI model names to pipeline model names
    const modelMapping = {
      'flux': 'flux-schnell',
      'flux-schnell': 'flux-schnell',
      'flux-dev': 'flux-dev',
      'sdxl_turbo': 'sdxl-turbo',
      'sdxl-turbo': 'sdxl-turbo',
      'sdxl': 'stable-diffusion-xl-base-1.0',
      'sdxl_base': 'stable-diffusion-xl-base-1.0',
      'stable-diffusion-xl-base-1.0': 'stable-diffusion-xl-base-1.0',
      'sdxl_refiner': 'stable-diffusion-xl-refiner-1.0',
      'stable-diffusion-xl-refiner-1.0': 'stable-diffusion-xl-refiner-1.0',
      'stable_diffusion': 'stable-diffusion-v1-5',
      'sd_v1_5': 'stable-diffusion-v1-5',
      'stable-diffusion-v1-5': 'stable-diffusion-v1-5',
      'sd_v2_1': 'stable-diffusion-2-1',
      'stable-diffusion-2-1': 'stable-diffusion-2-1'
    };
    
    const pipelineModel = modelMapping[model] || model;
    
    // Create full output path
    const timestamp = Date.now();
    const imageId = crypto.randomUUID();
    const outputDir = path.join(app.getAppPath(), 'output');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    const fullOutputPath = outputPath || path.join(outputDir, `generated_${timestamp}_${imageId}.png`);
    
    // Send loading progress
    event.sender.send('image-generation-progress', {
      progress: 20,
      status: `Loading ${pipelineModel} model...`
    });
    
    // Prepare settings for pipeline
    const pipelineSettings = {
      model: pipelineModel,
      ...settings
    };
    
    // Send initial progress
    event.sender.send('image-generation-progress', {
      progress: 5,
      status: 'Starting image generation...',
      stage: 'initialization',
      message: 'Initializing generation pipeline'
    });
    
    // Progress callback for detailed tracking
    const progressCallback = (progressData) => {
      try {
        // Handle preview image - can be either path or base64 data
        let preview_base64 = null;
        if (progressData.preview_image) {
          // Direct base64 data from Python script
          preview_base64 = progressData.preview_image;
          logger.info(`Received preview image, base64 length: ${preview_base64.length}`);
        } else if (progressData.preview_path) {
          // File path (legacy support)
          try {
            const previewData = fs.readFileSync(progressData.preview_path);
            preview_base64 = previewData.toString('base64');
          } catch (previewError) {
            logger.warn(`Failed to read preview image: ${previewError.message}`);
          }
        }

        // Send enhanced progress data to frontend
        event.sender.send('image-generation-progress', {
          progress: Math.round(progressData.overall_progress || 0),
          status: progressData.message || 'Generating...',
          stage: progressData.stage,
          step: progressData.step,
          total: progressData.total,
          stage_progress: Math.round(progressData.stage_progress || 0),
          overall_progress: Math.round(progressData.overall_progress || 0),
          preview_base64: preview_base64,
          message: progressData.message,
          timestamp: progressData.timestamp
        });
      } catch (error) {
        logger.warn(`Progress callback error: ${error.message}`);
      }
    };
    
    const result = await pipelineManager.generateImage(prompt, fullOutputPath, pipelineSettings, progressCallback);
    
    logger.info(`Pipeline result received: success=${result.success}, output_path=${result.output_path}`);
    
    if (result.success) {
      logger.info('Processing successful result...');
      
      // Send completion progress
      event.sender.send('image-generation-progress', {
        progress: 90,
        status: 'Processing result...'
      });
      
      // Read the generated image as base64
      let imageBase64;
      try {
        logger.info(`Checking if image file exists: ${result.output_path}`);
        if (!fs.existsSync(result.output_path)) {
          throw new Error(`Generated image file not found: ${result.output_path}`);
        }
        
        logger.info('Reading image file as base64...');
        const imageData = fs.readFileSync(result.output_path);
        imageBase64 = imageData.toString('base64');
        logger.info(`Image converted to base64 successfully (${imageBase64.length} chars)`);
        
        // Send final result
        logger.info('Sending final result to frontend...');
        event.sender.send('image-generation-progress', {
          progress: 100,
          status: 'Generation complete!',
          image_base64: imageBase64
        });
        
        logger.info('Final result sent to frontend successfully');
      } catch (fileError) {
        logger.error(`Failed to read generated image: ${fileError.message}`);
        throw new Error(`Failed to read generated image: ${fileError.message}`);
      }
      
      return {
        success: true,
        image_base64: imageBase64,
        output_path: result.output_path,
        model: pipelineModel,
        prompt: prompt,
        settings: pipelineSettings
      };
    } else {
      logger.error(`Pipeline result indicated failure: ${result.error}`);
      
      // Send error
      event.sender.send('image-generation-progress', {
        progress: 0,
        status: 'Generation failed',
        error: result.error
      });
      
      throw new Error(result.error || 'Image generation failed');
    }
    
  } catch (error) {
    logger.error('Failed to generate image (streaming):', error);
    logger.error('Error stack:', error.stack);
    
    // Send error progress
    event.sender.send('image-generation-progress', {
      progress: 0,
      status: 'Generation failed',
      error: error.message
    });
    
    return {
      success: false,
      error: error.message
    };
  }
});

// Note: Removed refresh-pipeline-templates handler - templates are now embedded configs

ipcMain.handle('install-dependencies', async (event, pipelineName, component, name) => {
  logger.info(`IPC: install-dependencies called for ${pipelineName} -> ${component} -> ${name || 'all'}`);
  try {
    // Await the installation to properly handle errors
    await dependencyManager.installDependencies(pipelineName, component, name);
    return { success: true };
  } catch (error) {
    logger.error(`Installation failed for ${pipelineName} (${component}:${name}):`, error);
    return { success: false, error: error.message };
  }
});

// Create project from generated image
ipcMain.handle('create-project-from-image', async (event, data) => {
  logger.info(`IPC: create-project-from-image called for: ${data.name}`);

  try {
    const { name, type, image_base64, prompt, model, settings } = data;

    if (!image_base64) {
      throw new Error('Missing image_base64');
    }

    // Create project ID and directories
    const projectId = crypto.randomUUID();
    const projectFilesDir = path.join(PROJECTS_DIR, 'files', projectId);
    const thumbnailsDir = path.join(PROJECTS_DIR, 'thumbnails');

    // Ensure directories exist
    fs.mkdirSync(projectFilesDir, { recursive: true });
    fs.mkdirSync(thumbnailsDir, { recursive: true });
    fs.mkdirSync(METADATA_DIR, { recursive: true });

    // Save the generated image
    const timestamp = Date.now();
    const imageFileName = `generated_image_${timestamp}.png`;
    const thumbnailFileName = `thumb_${projectId}.png`;

    const imageFilePath = path.join(projectFilesDir, imageFileName);
    const thumbnailFilePath = path.join(thumbnailsDir, thumbnailFileName);

    // Decode and save the image
    const imageBuffer = Buffer.from(image_base64, 'base64');
    fs.writeFileSync(imageFilePath, imageBuffer);
    fs.writeFileSync(thumbnailFilePath, imageBuffer); // Use same image as thumbnail

    // Create project metadata
    const projectData = {
      id: projectId,
      name: name || 'Generated Image Project',
      type: type || 'image-generation',
      created_at: new Date().toISOString(),
      prompt: prompt,
      model: model,
      settings: settings,
      files: {
        generated_image: imageFileName,
        thumbnail: thumbnailFileName
      }
    };

    // Save project metadata
    const metadataPath = path.join(METADATA_DIR, `${projectId}.json`);
    fs.writeFileSync(metadataPath, JSON.stringify(projectData, null, 2));

    logger.info(`Project created successfully: ${projectId}`);

    return {
      success: true,
      project: projectData
    };

  } catch (error) {
    logger.error('Error creating project from image:', error);
    return {
      success: false,
      error: error.message
    };
  }
});

// Create project from generated 3D model
ipcMain.handle('create-project-from-3d-model', async (event, data) => {
  logger.info(`IPC: create-project-from-3d-model called for: ${data.name}`);

  try {
    const { name, type, modelFilePath, videoFilePath, originalImagePath, prompt, settings, generationStats } = data;

    if (type !== 'image-generation' && !modelFilePath) {
      throw new Error('Missing modelFilePath');
    }

    // Create project ID and directories
    const projectId = crypto.randomUUID();
    const projectFilesDir = path.join(PROJECTS_DIR, 'files', projectId);
    const thumbnailsDir = path.join(PROJECTS_DIR, 'thumbnails');

    // Ensure directories exist
    fs.mkdirSync(projectFilesDir, { recursive: true });
    fs.mkdirSync(thumbnailsDir, { recursive: true });
    fs.mkdirSync(METADATA_DIR, { recursive: true });

    // Copy model file
    const modelFileName = `model.glb`;
    const modelDestPath = path.join(projectFilesDir, modelFileName);

    // Resolve the model file path (it might be relative to app path)
    const sourceModelPath = path.isAbsolute(modelFilePath) ? modelFilePath : path.join(app.getAppPath(), modelFilePath);

    if (!fs.existsSync(sourceModelPath)) {
      throw new Error(`Model file not found: ${sourceModelPath}`);
    }

    fs.copyFileSync(sourceModelPath, modelDestPath);

    // Copy video file if provided
    let videoFileName = null;
    if (videoFilePath) {
      videoFileName = `video.mp4`;
      const videoDestPath = path.join(projectFilesDir, videoFileName);
      const sourceVideoPath = path.isAbsolute(videoFilePath) ? videoFilePath : path.join(app.getAppPath(), videoFilePath);

      if (fs.existsSync(sourceVideoPath)) {
        fs.copyFileSync(sourceVideoPath, videoDestPath);
      }
    }

    // Copy original image if provided
    let originalImageFileName = null;
    if (originalImagePath) {
      const originalImageExt = path.extname(originalImagePath) || '.png';
      originalImageFileName = `original_image${originalImageExt}`;
      const originalImageDestPath = path.join(projectFilesDir, originalImageFileName);
      const sourceOriginalImagePath = path.isAbsolute(originalImagePath) ? originalImagePath : path.join(app.getAppPath(), originalImagePath);

      if (fs.existsSync(sourceOriginalImagePath)) {
        fs.copyFileSync(sourceOriginalImagePath, originalImageDestPath);
      }
    }

    // Create thumbnail from original image or use a default
    const thumbnailFileName = `thumb_${projectId}.jpg`;
    const thumbnailFilePath = path.join(thumbnailsDir, thumbnailFileName);

    if (originalImagePath && fs.existsSync(originalImagePath)) {
      // Use original image as thumbnail
      fs.copyFileSync(originalImagePath, thumbnailFilePath);
    } else {
      // Create a simple placeholder thumbnail
      const placeholderBuffer = Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGA4nEKtAAAAABJRU5ErkJggg==', 'base64');
      fs.writeFileSync(thumbnailFilePath, placeholderBuffer);
    }

    // Merge all settings into generationStats.settings
    const mergedSettings = { ...(generationStats?.settings || {}), ...(settings || {}) };
    // Try to get image resolution from the original image (if available)
    let width, height;
    if (originalImagePath) {
      const imagePath = path.isAbsolute(originalImagePath) ? originalImagePath : path.join(app.getAppPath(), originalImagePath);
      if (fs.existsSync(imagePath)) {
        try {
          const dimensions = imageSize(imagePath);
          width = dimensions.width;
          height = dimensions.height;
        } catch (e) {
          logger.warn('Could not determine image resolution:', e);
        }
      }
    }
    // Merge fileInfo
    const mergedFileInfo = {
      ...(generationStats?.fileInfo || {}),
      ...(width && height ? { width, height } : {})
    };
    // Compose newGenerationStats
    const newGenerationStats = {
      ...generationStats,
      settings: mergedSettings,
      fileInfo: mergedFileInfo
    };

    // Create project metadata
    const projectData = {
      id: projectId,
      name: name || 'Generated 3D Model',
      type: type || 'image-to-3d',
      created_at: new Date().toISOString(),
      status: 'completed',
      prompt: prompt,
      original_image_path: originalImageFileName,
      files: {
        model: modelFileName,
        video: videoFileName,
        thumbnail: thumbnailFileName
      },
      generationStats: newGenerationStats,
      settings: settings || {}
    };

    // Save project metadata
    const metadataPath = path.join(METADATA_DIR, `${projectId}.json`);
    fs.writeFileSync(metadataPath, JSON.stringify(projectData, null, 2));

    logger.info(`3D Model project created successfully: ${projectId}`);

    return {
      success: true,
      project: projectData
    };

  } catch (error) {
    logger.error('Error creating project from 3D model:', error);
    return {
      success: false,
      error: error.message
    };
  }
});

// Add IPC handler to list available upscaling models
ipcMain.handle('getUpscaleModels', async () => {
  try {
    const upscaylModelsDir = path.join(__dirname, '../../utils/helpers/upscayl-main/resources/models');

    // Check if Upscayl models directory exists
    if (!fs.existsSync(upscaylModelsDir)) {
      logger.warn('Upscayl models directory not found, falling back to installed models');

      // Fallback to checking our installed models
      const modelsDir = path.join(__dirname, '../../models/upscaling');
      if (fs.existsSync(modelsDir)) {
        const modelDirs = await fs.promises.readdir(modelsDir);
        const availableModels = [];

        for (const modelDir of modelDirs) {
          const modelPath = path.join(modelsDir, modelDir);
          try {
            const stat = await fs.promises.stat(modelPath);
            if (stat.isDirectory()) {
              const files = await fs.promises.readdir(modelPath);
              const hasWeights = files.some(f => /\.(pth|onnx|bin|param)$/i.test(f));
              if (hasWeights) {
                availableModels.push(modelDir);
              }
            }
          } catch (err) {
            continue;
          }
        }

        logger.info(`Found ${availableModels.length} fallback models: ${availableModels.join(', ')}`);
        return availableModels.length > 0 ? availableModels : ['swinir-real-sr-x4'];
      }

      return ['swinir-real-sr-x4']; // ultimate fallback
    }

    // Get available Upscayl models
    const files = await fs.promises.readdir(upscaylModelsDir);
    const modelNames = new Set();

    // Extract model names from .bin/.param file pairs
    for (const file of files) {
      if (file.endsWith('.bin')) {
        const modelName = file.replace('.bin', '');
        const paramFile = modelName + '.param';
        if (files.includes(paramFile)) {
          modelNames.add(modelName);
        }
      }
    }

    // Map Upscayl models back to our naming convention for UI consistency
    const modelMapping = {
      'upscayl-standard-4x': 'swinir-real-sr-x4',
      'upscayl-lite-4x': 'swinir-m-x4',
      'remacri-4x': 'realesrgan-x4plus',
      'digital-art-4x': 'realesrgan-x4plus-anime',
      'ultrasharp-4x': '4xlsdir',
      'high-fidelity-4x': 'high-fidelity-4x',
      'ultramix-balanced-4x': 'ultramix-balanced-4x'
    };

    const availableModels = Array.from(modelNames)
      .map(name => modelMapping[name] || name)
      .filter(Boolean);

    logger.info(`Mapped models: ${JSON.stringify(Array.from(modelNames))} -> ${JSON.stringify(availableModels)}`);

    logger.info(`Found ${availableModels.length} Upscayl models: ${availableModels.join(', ')}`);
    return availableModels.length > 0 ? availableModels : ['swinir-real-sr-x4'];
  } catch (e) {
    logger.error('Error getting upscale models:', e);
    return ['swinir-real-sr-x4']; // fallback
  }
});

// Update upscale-image handler to accept any model name/path and additional parameters
ipcMain.handle('upscale-image', async (event, {
  input,
  output,
  model,
  scale = 4,
  compression = 0,
  format = 'png',
  gpuId = null,
  customWidth = null,
  tileSize = 400,
  ttaMode = false
}) => {
  logger.info(`IPC: upscale-image called with:`, {
    inputLength: input?.length,
    output,
    model,
    scale,
    compression,
    format,
    gpuId,
    customWidth,
    tileSize,
    ttaMode
  });

  // Dependencies should be installed via dependency manager
  logger.info('Using ImageUpscaling pipeline environment for upscaling');

  // Check if input is still a data URL and convert it if needed
  let actualInput = input;
  if (input && input.startsWith('data:')) {
    logger.warn('Input is still a data URL, converting to temp file...');
    try {
      actualInput = await new Promise((resolve, reject) => {
        const matches = input.match(/^data:([A-Za-z-+\/]+);base64,(.+)$/);
        if (!matches) {
          reject(new Error('Invalid data URL format'));
          return;
        }

        const mimeType = matches[1];
        const base64Data = matches[2];
        const buffer = Buffer.from(base64Data, 'base64');

        // Determine file extension from MIME type
        let extension = 'png';
        if (mimeType.includes('jpeg') || mimeType.includes('jpg')) {
          extension = 'jpg';
        } else if (mimeType.includes('webp')) {
          extension = 'webp';
        }

        const tempDir = path.join(__dirname, '../../temp');
        const filename = `upscale_input_${Date.now()}.${extension}`;
        const filePath = path.join(tempDir, filename);

        fs.promises.mkdir(tempDir, { recursive: true }).then(() => {
          return fs.promises.writeFile(filePath, buffer);
        }).then(() => {
          resolve(filePath);
        }).catch(reject);
      });
      logger.info(`Converted data URL to temp file: ${actualInput}`);
    } catch (error) {
      logger.error('Failed to convert data URL to temp file:', error);
      return { success: false, error: 'Failed to process input image' };
    }
  }

  // Determine model path
  let modelPath = model;
  if (!modelPath.includes('/') && !modelPath.includes('\\')) {
    // If just a filename, resolve from models/upscaling
    modelPath = path.join(__dirname, '../../models/upscaling', model);
  }

  return new Promise((resolve, reject) => {
    // Use Upscayl native binary instead of Python scripts
    const upscaylBinPath = path.join(__dirname, '../../utils/helpers/upscayl-main/resources/win/bin/upscayl-bin.exe');
    const modelsPath = path.join(__dirname, '../../utils/helpers/upscayl-main/resources/models');

    // Check if Upscayl binary exists
    if (!fs.existsSync(upscaylBinPath)) {
      logger.error(`Upscayl binary not found at: ${upscaylBinPath}`);
      reject({ success: false, error: 'Upscayl binary not found. Please ensure Upscayl is properly installed.' });
      return;
    }

    logger.info(`Using Upscayl binary: ${upscaylBinPath}`);
    logger.info(`Models path: ${modelsPath}`);

    // Map our model names to Upscayl model names
    const modelMapping = {
      'swinir-real-sr-x4': 'upscayl-standard-4x',
      'swinir-m-x4': 'upscayl-lite-4x',
      'realesrgan-x4plus': 'remacri-4x',
      'realesrgan-x4plus-anime': 'digital-art-4x',
      '4xlsdir': 'ultrasharp-4x',
      'high-fidelity-4x': 'high-fidelity-4x',
      'ultramix-balanced-4x': 'ultramix-balanced-4x'
    };

    const modelName = path.basename(modelPath);
    const upscaylModel = modelMapping[modelName] || 'upscayl-standard-4x';

    logger.info(`Mapping model ${modelName} to Upscayl model: ${upscaylModel}`);

    // Build Upscayl arguments following their format
    const args = [
      '-i', actualInput,           // Input image
      '-o', output,                // Output image
      '-m', modelsPath,            // Models path
      '-n', upscaylModel,          // Model name
      '-f', format                 // Output format
    ];

    // Add scale if different from model default
    if (scale && scale !== 4) {
      args.push('-s', scale.toString());
    }

    // Add optional parameters
    if (gpuId) {
      args.push('-g', gpuId);
    }
    if (customWidth && customWidth > 0) {
      args.push('-w', customWidth.toString());
    }
    if (compression > 0) {
      args.push('-c', compression.toString());
    }
    if (tileSize && tileSize !== 400) {
      args.push('-t', tileSize.toString());
    }
    if (ttaMode) {
      args.push('-x');
    }

    logger.info(`Upscayl arguments: ${JSON.stringify(args)}`);

    logger.info('Starting Upscayl process...');
    const proc = spawn(upscaylBinPath, args);
    let errorOutput = '';
    let hasOutput = false;
    let progressValue = 0;

    let outputBuffer = '';

    proc.stderr.on('data', (data) => {
      hasOutput = true;
      outputBuffer += data.toString();

      // Process complete lines
      const lines = outputBuffer.split(/\r?\n/);
      outputBuffer = lines.pop() || ''; // Keep incomplete line in buffer

      for (const line of lines) {
        if (line.trim()) {
          logger.info('[upscayl-bin]', line.trim());

          // Try to extract progress percentage from Upscayl output
          const progressMatch = line.match(/(\d+(?:\.\d+)?)%/);
          if (progressMatch) {
            progressValue = parseFloat(progressMatch[1]);
            event.sender.send('upscale-image-progress', {
              stage: 'upscaling',
              step: 1,
              total: 1,
              progress: progressValue,
              message: `Upscaling... ${progressValue.toFixed(1)}%`
            });
          } else if (line.includes('Resizing')) {
            event.sender.send('upscale-image-progress', {
              stage: 'resizing',
              step: 1,
              total: 1,
              progress: 95,
              message: 'Resizing and converting...'
            });
          } else if (line.includes('Successfully')) {
            event.sender.send('upscale-image-progress', {
              stage: 'complete',
              step: 1,
              total: 1,
              progress: 100,
              message: 'Upscaling completed successfully!'
            });
          }
        }
      }
    });

    proc.stdout.on('data', (data) => {
      hasOutput = true;
      logger.info('[upscayl-bin][stdout]', data.toString());
    });

    proc.on('close', (code) => {
      logger.info(`Upscayl process closed with code ${code}, hasOutput: ${hasOutput}`);
      if (code === 0) {
        // Check if output file exists and wait a moment for file system to sync
        setTimeout(() => {
          if (fs.existsSync(output)) {
            const fileSize = fs.statSync(output).size;
            logger.info(`Upscayl output file created successfully: ${output} (${fileSize} bytes)`);
            resolve({ success: true, output });
          } else {
            logger.error(`Upscayl completed but output file not found: ${output}`);
            reject({ success: false, error: 'Upscayl completed but output file was not created' });
          }
        }, 500); // Wait 500ms for file system to sync
      } else {
        logger.error(`Upscayl binary exited with code ${code}`);
        logger.error(`Error output: ${errorOutput}`);
        reject({ success: false, error: errorOutput || `Upscayl failed with code ${code}` });
      }
    });

    proc.on('error', (err) => {
      logger.error('Failed to start Upscayl process:', err);
      reject({ success: false, error: err.message });
    });

    // Add timeout to detect if process hangs (Upscayl can take longer)
    setTimeout(() => {
      if (!hasOutput) {
        logger.error('No output from Upscayl process after 30 seconds - killing process');
        proc.kill();
        reject({ success: false, error: 'Upscayl process timed out - no output received' });
      }
    }, 30000);
  });
});

// IPC handlers for upscaling support
ipcMain.handle('get-temp-file-path', async (event, filename) => {
  const tempDir = path.join(__dirname, '../../temp');
  await fs.promises.mkdir(tempDir, { recursive: true });
  return path.join(tempDir, filename);
});

ipcMain.handle('save-data-url-to-temp-file', async (event, dataUrl) => {
  try {
    const matches = dataUrl.match(/^data:([A-Za-z-+\/]+);base64,(.+)$/);
    if (!matches) {
      throw new Error('Invalid data URL format');
    }

    const mimeType = matches[1];
    const base64Data = matches[2];
    const buffer = Buffer.from(base64Data, 'base64');

    // Determine file extension from MIME type
    let extension = 'png';
    if (mimeType.includes('jpeg') || mimeType.includes('jpg')) {
      extension = 'jpg';
    } else if (mimeType.includes('webp')) {
      extension = 'webp';
    }

    const tempDir = path.join(__dirname, '../../temp');
    await fs.promises.mkdir(tempDir, { recursive: true });

    const filename = `temp_${Date.now()}.${extension}`;
    const filePath = path.join(tempDir, filename);

    await fs.promises.writeFile(filePath, buffer);
    logger.info(`Saved data URL to temp file: ${filePath}`);

    return filePath;
  } catch (error) {
    logger.error('Error saving data URL to temp file:', error);
    throw error;
  }
});

ipcMain.handle('save-screenshot-to-project', async (event, projectId, dataUrl) => {
  try {
    if (!projectId || !dataUrl) throw new Error('Missing projectId or dataUrl');
    const thumbnailsDir = path.join(PROJECTS_DIR, 'thumbnails');
    fs.mkdirSync(thumbnailsDir, { recursive: true });
    const thumbFileName = `thumb_${projectId}.png`;
    const thumbFilePath = path.join(thumbnailsDir, thumbFileName);
    // Remove data URL prefix
    const base64 = dataUrl.replace(/^data:image\/png;base64,/, '');
    fs.writeFileSync(thumbFilePath, Buffer.from(base64, 'base64'));
    // Update project metadata
    const metadataPath = path.join(METADATA_DIR, `${projectId}.json`);
    if (fs.existsSync(metadataPath)) {
      const projectData = JSON.parse(fs.readFileSync(metadataPath, 'utf-8'));
      if (!projectData.files) projectData.files = {};
      projectData.files.thumbnail = thumbFileName;
      projectData.thumbnail_url = `projects/thumbnails/${thumbFileName}`;
      fs.writeFileSync(metadataPath, JSON.stringify(projectData, null, 2));
    }
    return { success: true, path: thumbFileName };
  } catch (error) {
    logger.error('Error saving screenshot to project:', error);
    return { success: false, error: error.message };
  }
});