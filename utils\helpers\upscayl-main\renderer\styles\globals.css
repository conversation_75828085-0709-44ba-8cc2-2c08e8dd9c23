@font-face {
  font-family: "Poppins";
  src: url("../fonts/poppins/Poppins-Regular.ttf");
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: "Poppins";
  src: url("../fonts/poppins/Poppins-Medium.ttf");
  font-weight: 500;
  font-style: normal;
}
@font-face {
  font-family: "Poppins";
  src: url("../fonts/poppins/Poppins-SemiBold.ttf");
  font-weight: 600;
  font-style: normal;
}
@font-face {
  font-family: "Poppins";
  src: url("../fonts/poppins/Poppins-Bold.ttf");
  font-weight: 700;
  font-style: normal;
}
@font-face {
  font-family: "Poppins";
  src: url("../fonts/poppins/Poppins-ExtraBold.ttf");
  font-weight: 800;
  font-style: normal;
}
@font-face {
  font-family: "Poppins";
  src: url("../fonts/poppins/Poppins-Black.ttf");
  font-weight: 900;
  font-style: normal;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply select-none border-border;
    font-family: "Poppins", sans-serif;
  }
  body {
    @apply bg-background text-foreground;
  }
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
  .hide-scrollbar::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  .hide-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-transparent;
  }

  ::-webkit-scrollbar {
    @apply w-3;
  }

  ::-webkit-scrollbar-track {
    @apply rounded-full bg-base-300/30;
  }

  ::-webkit-scrollbar-thumb {
    @apply rounded-full bg-base-content/20;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply rounded-full bg-base-content/50;
  }
}

@layer utilities {
  .animate {
    @apply transition-all duration-300 ease-in-out;
  }
  .step-heading {
    @apply mb-2 font-semibold;
  }

  .react-select-container {
    @apply w-40;
  }
  .full-width {
    @apply w-full;
  }
  .react-select-container .react-select__control {
    @apply h-12 cursor-pointer rounded-btn !border-0 !border-none !border-transparent bg-primary shadow-none;
  }

  .react-select-container .react-select__control--menu-is-open {
    @apply bg-primary text-primary-content;
  }

  .react-select-container .react-select__single-value {
    @apply text-sm font-semibold uppercase text-primary-content;
  }

  .react-select-container .react-select__input-container {
    @apply text-sm font-semibold uppercase text-primary-content;
  }

  .react-select-container .react-select__menu {
    @apply rounded-lg bg-primary p-1 text-sm font-semibold uppercase;
  }

  .react-select-container .react-select__option {
    @apply my-1 cursor-pointer break-all rounded-md bg-primary text-primary-content hover:bg-primary-content/20 hover:text-primary-content;
  }

  .react-select-container .react-select__option--is-selected {
    @apply bg-accent text-accent-content;
  }

  .outline-title {
    text-shadow:
      0px 0px 10px rgba(255, 255, 255, 0.5),
      0px 0px 10px rgba(255, 255, 255, 1);
  }
  .outline-title-none {
    text-shadow: none;
  }
}

.zoom-100 {
  transform: scale(1);
}
.zoom-125 {
  transform: scale(1.25);
}
.zoom-150 {
  transform: scale(1.5);
}
.zoom-175 {
  transform: scale(1.75);
}
.zoom-200 {
  transform: scale(2);
}

.animate-step-in {
  animation: animate-step-in 0.6s cubic-bezier(0.07, 0.43, 0.02, 1);
}

[data-theme="upscayl"] .btn {
  @apply h-10 !min-h-min px-3 py-2 font-medium normal-case;
}

[data-theme="upscayl"] input[type="range"] {
  @apply rounded-full;
}

[data-theme="upscayl"] .react-select-container .react-select__control {
  @apply h-10 cursor-pointer rounded-btn !border-0 !border-none !border-transparent bg-primary shadow-none;
}

[data-theme="upscayl"] .react-select-container .react-select__single-value {
  @apply font-medium normal-case text-primary-content;
}

[data-theme="upscayl"] .react-select-container .react-select__input-container {
  @apply text-xs font-medium normal-case text-primary-content;
}

[data-theme="upscayl"] .react-select-container .react-select__menu {
  @apply rounded-lg bg-primary p-1 font-medium normal-case;
}

.__rcs-handle-button {
  @apply border-white;
}

.mac-titlebar {
  -webkit-app-region: drag;
}

.checkerboard {
  background-color: rgba(255, 255, 255);
  background-image: linear-gradient(
      45deg,
      #000 25%,
      transparent 25%,
      transparent 75%,
      #000 75%,
      #000
    ),
    linear-gradient(
      -45deg,
      #000 25%,
      transparent 25%,
      transparent 75%,
      #000 75%,
      #000
    );
  background-size: 40px 40px;
  background-repeat: repeat;
}

.spinner {
  -webkit-animation: sk-rotateplane 2.2s infinite ease-in-out;
  animation: sk-rotateplane 2.2s infinite ease-in-out;
}

@keyframes animate-step-in {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  80% {
    opacity: 1;
  }
  100% {
    opacity: 1;
    transform: translateY(0px);
  }
}

@-webkit-keyframes sk-rotateplane {
  0% {
    -webkit-transform: perspective(120px);
  }
  50% {
    -webkit-transform: perspective(120px) rotateY(360deg);
  }
  100% {
    -webkit-transform: perspective(120px) rotateY(360deg) rotateX(360deg);
  }
}

@keyframes sk-rotateplane {
  0% {
    transform: perspective(120px) rotateX(0deg) rotateY(0deg);
    -webkit-transform: perspective(120px) rotateX(0deg) rotateY(0deg);
  }
  50% {
    transform: perspective(120px) rotateX(-360.1deg) rotateY(0deg);
    -webkit-transform: perspective(120px) rotateX(-360.1deg) rotateY(0deg);
  }
  100% {
    transform: perspective(120px) rotateX(-360deg) rotateY(-179.9deg);
    -webkit-transform: perspective(120px) rotateX(-360deg) rotateY(-359.9deg);
  }
}
