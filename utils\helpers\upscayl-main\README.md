<div align="center">

  # v2.15 is out! 🥳 [Download Now ⬇️](https://github.com/upscayl/upscayl/releases/latest)

<h3>Special thanks to our sponsors:</h3>
<a href="https://www.warp.dev/upscayl">
   <div>
   <img alt="Warp sponsorship" width="500" src="https://github.com/user-attachments/assets/7626b7c6-e673-45ee-ac9a-3392e643f54f">
   </div>
   <b>Warp, the intelligent terminal for developers</b>
   <div>
      <sup>Use images as AI context in your terminal!</sup>
   </div>
</a>

#

<a href="https://github.com/upscayl/upscayl/releases/latest">
  
  ![Frame 111](https://github.com/upscayl/upscayl/assets/25067102/d1b4af3c-aade-4bc9-97d0-cf88db679931)
</a>

<a href="https://upscayl.org/#download">
  <img src="https://img.shields.io/github/downloads/upscayl/upscayl/total.svg?style=for-the-badge&logo=data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IB2cksfwAAAAlwSFlzAAALEwAACxMBAJqcGAAACIJJREFUeJzN21lUFFcaB3A9mbzNTB7zlDPzMHNyziwJIiCrssgi0OwoNPvWbLKoNCJL2BHjjggIaAybQDCoBBFDnMnMZBLNiVHUBE3c0bivGMf1m+82VHV1d61NL8U5v4eubm7V96/btdzqO2eOwN97i6Lm2rhH/wFlohF0Ef2KXiOQkefoF/QV+hDZot8I1cf5R/4Z+aO96JEMCjQmEBJGOvqd1OJ/ixpm9rS1C5mtV+gw+pPY4v+KxnUa8VCCnWI5uGZtB//qQ1D66W2oOPAQKoa4JbdPgPeH/+HkuryFbj9AtYG3LTE+GLwLhV0XIG3DEfBOqIL53gn6QdxHke+7R/EW/xc0Qf+TZww4RBaBR9kB8Nl0DHy3Hoew5jOw9gsQpOq+qvk8l4X5u+iNU2RtFdWmWHVjr6C4/xqErWgFe98UZggPUBxftz+p3esx4JKxDXw2HtXZcPMEsAXqyIabMASNsdeQ1XgUHJakMUN4jP6uX/wbaCuzePfVvawbbq4AanFjxagzAvnKLghIZ4ZwHL3NDMAPPaG6vWtmI+eGmyOAwMwtUPP5K061fMa46AaX23pCP4Sq+YsjSfGa8/xe6g2HpUX4fT9q8QCqD78U53NDNawMg4wu7WMGcAG9Rfb+H22o8zwe7T3K9vNuuDkCCMjcDFWjL9gdNtZLGhVe6b774BiYwQwhmQSQSy0gpzrqaG/RADI2Q+Xoc5FeiMIWJlm+dE03M4Ajc2YuEjQLXLOaeDfanAGUH3qmUSHoufEwwJWd55kBTJEArtAbUjMK4S0/8IpuOwvFQ1OC0jov87azuKCD3hD/jE1QPvJMvEPGIQGWjzwFW684et0kgGfUizI8XQjt2ZLPngj2EiKt6wpvOynrDusEUHbwf7w+GDEd5+AcnQDoF+SS0hoBLEnfBKXDT3UdlKZMArfwVXILYCOUDP8qWakohsG6RcgwgGJs19RKNAyDk10P8MMAivDASVkzW58xPTHgKrsAVBjAgceGhvhMGYUEJLsAfFUboHD/Iyg8wG+1jsdGk2UA6v0PJXqko1ACWQZQsO8BeiiJmg9PeK7hK9kDSGqf0FzC8iGFCX2GSO24xPt+cEk/vV6ftPWwavA+emC8fYYKOLhwBUDG6qxxL0ACWPHpPXSftlKMQePIIgC3vHbtelPXQf7euzMhmIs2WJcwKwbgvf4rcE7dCLZ+2nG6+d6J4J1SD6mtpyAPg+CSPyv3aFYK4DtYuGI3zFtsMFxNm+ehBP+cZsjumYTcgTuS5Am6S7NKAJricU9zFU+H4BkDfpkNkNN/C3I+uc1v4LbkoAhnSwdAuj3fnmejXP8PWI4hGPhE321OXME5h62wbADOKRt0C8SuTro752vkFJIHGdhGdv9NAbcks2gAZIR5fkAmvQ57vxRIWjsCiyLV2mW+yRBfuQ/v0rRdc55nLCQ2fw9ZfTdNzqIBLK7/ku7+ntHFsKZvUvO+p7KUXq+DXyqoO89D5fAUhOQ20b1hWf0YZPTeoGXOVt8051COAIoGbkH5wae81IP3QNF4UlBqx8Xpz/dOgh05+GFRy1Z3Qs3oM84AyFOc9C3/1rwmyyPrRiF9zy+iZejrZefEFYA57gXIXnVSZNGnOU9lCRR8fA48sDcwvxbZ249BUHaDdsASP6vc8jWoeq6Dag+/dEksHMDaL15rHn8z17PAXwV2Pkn0a1uvWHAM0HlgAfZL0iBx1zlI67k2KyoD1zGAfMveDa7uuYx7P0bSaTBA3QGp3ZNmcM3yARARq3ZqLnLEFO8coYak3RcgpeuqBJPcunVZJYCq4ScQmtcsqviorccgqfMKq2ROV7W6+DlaIwCCHOVzWvCyOHwV2OJ5nnkhZIffeX/s9vG7fobEjsvSYACJHIGxsVoAlOqRpxBbPkCv1y22HGJ2nIGEjkssLguSGphjiJUDIJhDYosS6yD+44ssLs1aAgvOAAoHbgo+aSnAC6FQvBoUQh6O8rUTVzusE0AsHvT0xe2+aBaOeJ/Bcyn8Ha+w5tOifpejwh7A187CvJ2MAGoh5qMLtFhjsATIZQF/AOYdEaLHBxhjggsTakG567woMZSPjCfLAKLx6M9FKYlwiLILwA0DiNr5k9GidfwsSIYB1MCy9nMm8BOrKD0LguUWQHwNLG07y+Mcu3ZufEHJLgBXDCCidYIWaeCsVtvsOQTnyi2Aagjf8SO7VmJCR8Qs6QfwnA5g3b+sEoALBhCGxfLhDMgI9opsnQBuUC8SGr+F7L4bvDJ6rkEyXk8LSeu8xNtOWJn2XsAlrhpCW34wiTA+O6bZMp5PkAD+S70gkw2scS/gElcFIdizTK5FK3SGYuPXzNvvlySAddQCn8RqzWQDSwfgjAEEN53m1izVGQ22ULwYP9BE4yQAMrvqBVlAppkU91+3SgCK7adoQU2mcNqAYtsJsGecAlEdCeBN9A21MHxlm2amhSUDcIqtgsDt46IpRDllwKtwD7P4e+gdasIEmRNIZldp5tiQIWrLBlAJAY0nBQU2jks3E5ofnuH09n4XmksF8Hs0Rr1Jhq3LBu9YNAB/7J7CTnIKoLAE599wHBwiCpnF30Hv688b+rMNY4IkGafPbxu3SACOMZWwpOF7EU4Y2sbPp/6f4BCuZhZPRL7rEsQ6cywWPWSGQMbuKoYemTmACvDDAk3Jd/O34K7u1u/2L1GTDdeUWptFUVQIj5mJOQZmQnRJLxTtuQIlQ1NmCUBMm2J413+p+TGGXVCu/l4nc50bOYvX6wl/s5meWqbfCNh6xWsed9sF5fAiDx/II3AuToHaR+bklyNC7QnCy1tb7ySD7Z1xF4WjNwSLZ4TwNqqymZ4tztWw3JFTXQc54Nm4h4iunf6b56kkQbyF0tARNCWDooSQi7oTqAa9g1/ruXw1/h/JQTLIQz2y/gAAAABJRU5ErkJggg==&labelColor=ede9fe&color=8e6bf6" width="200px" style="border-radius:50%"/>
</a>

  </br>
  </br>

<!--<a href="https://github.com/upscayl/upscayl/releases/latest">
  <img src="https://github.com/upscayl/upscayl/assets/25067102/6287fd40-2c91-4028-b1d6-3986e77d8211" width="200px" />
</a>-->

<a href="https://t.me/iamnayam">
  <img src="https://user-images.githubusercontent.com/25067102/209297095-a3db856f-b760-40bb-a68e-f3a3086e18c7.png" width="200px" />   
</a>      

<a href="https://x.com/upscayl">
  <img src="https://github.com/upscayl/upscayl/assets/25067102/917dcf6f-452b-43e6-95cd-2c6b0a47913d" width="200px" />
</a>

# 🆙 Upscayl

#### Free and Open Source AI Image Upscaler
Upscayl lets you enlarge and enhance low-resolution images using advanced AI algorithms.
Enlarge images without losing quality. It's almost like magic! 🎩🪄

**https://upscayl.org**

<video src="https://github.com/upscayl/upscayl/assets/25067102/ad2453b1-3c5a-4eb2-b992-4cf10e6a49f5" autoplay muted loop />

</div>

# Contents

- [👨‍💻 Installation](#-installation)
  - [🐧 Linux](#-linux)
  - [🍎 macOS](#-macos)
  - [🐌 Windows](#-windows)
- [👨‍🏫 Documentation - Tutorials and Guides](#-documentation---tutorials-and-guides)
- [⚖️ Demo Results (Before and After)](#%EF%B8%8F-results)
- [🤫 Roadmap](#-roadmap)
- [🛠 Developing Upscayl](#-development)
- [🤓 FAQ](#-faq)
- [🎁 Donate and support the project](#-donate)
- [❤ Credits](#-credits)

# 👨‍💻 Installation

> [!IMPORTANT]
> You'll need a Vulkan compatible GPU (Graphics Card) to upscale images. Many iGPUs (integrated graphics) do not work but, no harm in trying :)

## 🐧 Linux

  <a href="https://flathub.org/apps/org.upscayl.Upscayl">
    <img src="https://dl.flathub.org/assets/badges/flathub-badge-en.svg" height="50px"/>
  </a>

  <a href="https://appimage.github.io/Upscayl/">
    <img src="https://user-images.githubusercontent.com/25067102/191270389-9de37c0f-39a8-41f1-a659-8dd4e7b8ac28.png" height="50px"/>
  </a>

  <a href="https://aur.archlinux.org/packages/upscayl-bin">
    <img src="https://user-images.githubusercontent.com/25067102/191269445-87050a77-c304-4284-9ea0-699721309c59.png" height="50px"/>
  </a>

  <a href="https://snapcraft.io/upscayl/">
    <img src="https://snapcraft.io/static/images/badges/en/snap-store-black.svg" height="50px"/>
  </a>

  <a href="https://github.com/MrPenguin07/ebuilds">
    <img src="https://github.com/upscayl/upscayl/assets/25067102/322aebc5-91aa-4fc6-ac3a-d5a054449554" height="50px"/>
  </a>

Upscayl should be available on the software listings of most Linux operating systems. Your distro's Store app might also support the [Flatpak](https://flatpak.org/setup) or Snap version.

### 💼 Portable Method

1. Go to [releases section](https://github.com/upscayl/upscayl/releases/latest) or [our official website](https://upscayl.org/).
2. Download the `upscayl-x.x.x-linux.AppImage` file.
3. Right Click AppImage -> Go to Permissions tab -> Check 'allow file to execute' and then double click the file to run Upscayl.

*You can also choose to install using other formats like RPM (Fedora), DEB (Debian/Ubuntu based), and ZIP (Any x86 Linux OS).*

## 🍎 macOS
(MacOS 12 and later)

<a href="https://apps.apple.com/us/app/upscayl/id6468265473?mt=12">
  <img src="https://www.upscayl.org/appstore.svg" height="60px"/>
</a>

1. Go to [releases section](https://github.com/upscayl/upscayl/releases/latest) or [our official website](https://upscayl.org/).
2. Download the `upscayl-x.x.x-mac.dmg` file.
3. Double click dmg, drag Upscayl icon into Applications folder.
4. Open Finder, click 'Applications' tab in the left sidebar. Find Upscayl and right click on it. Select 'Open'.
5. In the window that appears, press 'Open' yet again.

### 🍺 Homebrew

`brew install --cask upscayl`

## 🐌 Windows
(Windows 10 and later)

1. Go to [releases section](https://github.com/upscayl/upscayl/releases/latest) or [our official website](https://upscayl.org/).
2. Download the `upscayl-x.x.x-win.exe` file.
3. Double click exe file to launch.
4. If you get a SmartScreen warning - click 'More Info' and then 'Run Anyway' OR press 'YES' on the unverified publisher dialog.
5. Follow the installation steps.
6. Profit!

# 👨‍🏫 Documentation - Tutorials and Guides

Check out our Documentation [here](https://docs.upscayl.org/).

- [Try out even more new models!](https://github.com/upscayl/custom-models)
- [Convert your own models](https://github.com/upscayl/upscayl/wiki/%F0%9F%96%A5%EF%B8%8F-Model-Conversion---Create-more-AI-models!)
- [Compatibility List](https://github.com/upscayl/upscayl/wiki/Compatibility-List)
- [Troubleshooting](https://github.com/upscayl/upscayl/wiki/Troubleshooting)

# ⚖️ Results

Check out Upscayl before/after comparisons [here](COMPARISONS.MD).

# 🤫 Roadmap

You can track all the progress here: https://github.com/orgs/upscayl/projects/1

- Fix bugs
- Make the whole world use FOSS (WIP 🚧)

# 🛠 Development

I recommend using Volta: https://volta.sh for installing Node.js.
Download and install volta, then do: `volta install node`.

## 🏃 Running
> [!NOTE]
> If you are not willing to install [git](https://git-scm.com/downloads), you can skip the first line, download [the source zip](https://github.com/upscayl/upscayl/archive/refs/heads/main.zip) and extract it to `upscayl` instead and carry on with the rest of the instructions.

```sh
git clone https://github.com/upscayl/upscayl
cd upscayl

# INSTALL DEPENDENCIES
npm install

# RUN THE DEVELOPMENT SERVER LOCALLY
## YOUR LOGS WILL NOW APPEAR IN THE TERMINAL
npm run start
```

## 🏗️ Building

```sh
# INSTALL DEPENDENCIES
npm install

# PACKAGE THE APP
npm run dist

# PUBLISH THE APP, MAKE SURE TO ADD GH_TOKEN= IN SHELL
# ONLY DO THIS IF YOU'RE A MAINTAINER
npm run publish-app
```

# 🤓 FAQ

- **How does Upscayl work?**
  - Upscayl uses AI models to enhance your images by guessing what the details could be. It uses Real-ESRGAN and Vulkan architecture to achieve this. [Our backend](https://github.com/upscayl/upscayl-ncnn) is fully open-source under the AGPLv3 license.
- **I don't see a drastic change in my upscaled image. Why is that?**
  - Upscayl can enhance low resolution images and images that are pixelated but it cannot de-blur or do focus adjustment on your image. If your image is out-of-focus or totally blurred, Upscayl is not the right tool for it. Please use images that are similar to the [examples we've given here.](COMPARISONS.MD)
- **Is there a CLI available?**
  - The CLI tool is called [upscayl-ncnn](https://github.com/upscayl/upscayl-ncnn).
- **Do I need a GPU for this to work?**
  - Yes, unfortunately. NCNN Vulkan requires a Vulkan-compatible GPU. Upscayl won't work with **most** iGPUs or CPUs. But hey, no harm in trying ;)
    - @Wyrdgirn has contributed a workaround for Windows and Linux in [#390](https://github.com/upscayl/upscayl/issues/390)! Nobody knows how to manipulate the macOS and Haiku frameworks...
- **I stopped the magic Batch Upscayl and my images haven't been processed, compressed, or are in the wrong scale!**
  - When a model doesn't support an action, Upscayl will finish upscayling all the images first before post-processing them. What this means is that you should simply **wait** for the process to finish.
- **How can I contribute?**
  - You can report issues, fix code and add features by submitting PRs, or donate! 😊
- **What's the GPU ID for?**
  - It is for selecting which GPU to use. The specific procedure is detailed in the [Wiki](https://github.com/upscayl/upscayl/wiki/Guide).
    - Note that for Windows systems, if Upscayl is not set to performance mode, the system may override this setting.
- **Where do I find more models?**
  -  More models can be taken from here: https://github.com/upscayl/custom-models

# 🎁 Donate

<a href="https://www.buymeacoffee.com/fossisthefuture">
  <img src="https://user-images.githubusercontent.com/25067102/154570688-9e143f2b-fee3-4b05-a9d2-a7a3013b2b51.png" />
<a/>

# ❤ Credits

- Real-ESRGAN for their wonderful research work.
[Real-ESRGAN: Copyright (c) 2021, Xintao Wang](https://github.com/xinntao/Real-ESRGAN/)
- @JanDeDinoMan, @xanderfrangos, @Fdawgs, @keturn for their code contributions
- @aaronliu0130 for providing community support :)
- Helaman for their [HFA2k model](https://openmodeldb.info/models/4x-HFA2k) (included as "High Fidelity")
- Foolhardy for their [Remacri model](https://openmodeldb.info/models/4x-Remacri).
- [Kim2091](https://upscale.wiki/wiki/User:Kim2091)	for their [Ultrasharp and Ultramix Balanced model](https://openmodeldb.info/models/4x-UltraSharp).
- @NicKoehler for their amazing logo :)
#

<div align="center">

Copyright © 2023 - **Upscayl**\
By Nayam Amarshe and TGS963\
Made with 🖱 & ⌨

</div>
