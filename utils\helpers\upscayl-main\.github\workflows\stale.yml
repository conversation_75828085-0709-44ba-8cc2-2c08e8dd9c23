name: 'Close stale issues and PRs'
on:
  schedule:
    - cron: '0 0 * * *'
  workflow_dispatch:

permissions:
  contents: write # only for delete-branch option
  issues: write
  pull-requests: write
  
jobs:
  stale:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@v9
        with:
          operations-per-run: 100
          stale-issue-message: 'This issue has been automatically marked as stale because it has not had recent activity. It will be closed if no further activity occurs. If this is still happening, please reply to indicate so.'
          stale-pr-message: 'This PR has been automatically marked as stale because it has not had recent activity. It will be closed if no further activity occurs. If you are still working on this, please reply to indicate so.'
          days-before-issue-stale: 30
          days-before-pr-stale: 45
          days-before-issue-close: 5
          days-before-pr-close: 10
          exempt-issue-labels: help-wanted, HIGH PRIORITY
          exempt-issue-assignees: aaro<PERSON><PERSON><PERSON>0130, <PERSON><PERSON><PERSON><PERSON><PERSON>, TGS963
