#!/usr/bin/env python3
"""
Simple test script to verify Python environment
"""
import sys
import os

print("PROGRESS:{\"stage\": \"test\", \"step\": 1, \"total\": 3, \"progress\": 33, \"message\": \"Python test script started\"}", flush=True)
print(f"Python version: {sys.version}")
print(f"Python executable: {sys.executable}")
print(f"Current working directory: {os.getcwd()}")

try:
    from PIL import Image
    print("PROGRESS:{\"stage\": \"test\", \"step\": 2, \"total\": 3, \"progress\": 66, \"message\": \"PIL import successful\"}", flush=True)
except ImportError as e:
    print(f"PROGRESS:{{\"stage\": \"error\", \"step\": 0, \"total\": 3, \"progress\": 0, \"message\": \"PIL import failed: {e}\"}}", flush=True)
    sys.exit(1)

print("PROGRESS:{\"stage\": \"test\", \"step\": 3, \"total\": 3, \"progress\": 100, \"message\": \"Python test completed successfully\"}", flush=True)
