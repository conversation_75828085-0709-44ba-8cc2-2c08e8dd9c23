<?xml version="1.0" encoding="UTF-8"?>
<component type="desktop-application">
  <id>org.upscayl.Upscayl</id>
  <name>Upscayl</name>
  <summary>Free and Open Source AI Image Upscaler</summary>

  <url type="homepage">https://upscayl.org</url>
  <url type="bugtracker">https://github.com/upscayl/upscayl/issues</url>
  <url type="donation">https://buymeacoffee.com/fossisthefuture</url>

  <metadata_license>FSFAP</metadata_license>
  <project_license>AGPL-3.0</project_license>

  <description>
    <p>
      Upscayl is a free and open source AI Image Upscaler.
    </p>
  </description>

  <content_rating type="oars-1.1" />

  <launchable type="desktop-id">org.upscayl.Upscayl.desktop</launchable>

  <screenshots>
    <screenshot>
      <caption>Banner</caption>
      <image>https://raw.githubusercontent.com/upscayl/upscayl.github.io/main/static/1.png</image>
    </screenshot>
    <screenshot>
      <caption>Science Fiction to Reality</caption>
      <image>https://raw.githubusercontent.com/upscayl/upscayl.github.io/main/static/2.png</image>
    </screenshot>
    <screenshot>
      <caption>5+ Upscaling Modes</caption>
      <image>https://raw.githubusercontent.com/upscayl/upscayl.github.io/main/static/3.png</image>
    </screenshot>
    <screenshot>
      <caption>Upscale Transparent PNGs</caption>
      <image>https://raw.githubusercontent.com/upscayl/upscayl.github.io/main/static/4.png</image>
    </screenshot>
    <screenshot>
      <caption>Upscale thousands of images at once</caption>
      <image>https://raw.githubusercontent.com/upscayl/upscayl.github.io/main/static/5.png</image>
    </screenshot>
    <screenshot>
      <caption>Fully Customizable</caption>
      <image>https://raw.githubusercontent.com/upscayl/upscayl.github.io/main/static/6.png</image>
    </screenshot>
  </screenshots>

  <releases>
    <release version="v2.9.1" date="2023-10-28">
      <description>
        <p>Changes</p>
        <ul>
          <li>Updated dependencies ⚡️</li>
          <li>Squashed bugs</li>
        </ul>
      </description>
    </release>
    <release version="v2.8.5" date="2023-09-21">
      <description>
        <p>Changes</p>
        <ul>
          <li>Updated electron version ⚡️</li>
          <li>Fixed scaling and conversion ⚖️</li>
          <li>Added 4:4:4 subsampling by default 🌈</li>
          <li>Added the option to switch off post-processing ⌥</li>
          <li>MacOS builds are now signed, which means no weird security warnings (App Store soon 😉)</li>
          <li>Fixed macOS issue with closed Windows (#471) ❌</li>
          <li>Fixed grainy picture issue (#474) 💥</li>
          <li>Fixed compression 🗜️</li>
        </ul>
      </description>
    </release>
    <release version="v2.8.1" date="2023-06-13">
      <description>
        <p>Changes</p>
        <ul>
            <li>We're introducing MacOS Apple Chip support in v2.8.1 🥳</li>
            <li>HUGE codebase refactor, to save us devs some time 🥇</li>
            <li>Batch upscale will now show you progress 💯</li>
            <li>We've integrated better compression and scaling ♻️</li>
            <li>Fixed the empty upscaled image bug 🗑️</li>
            <li>Added helpful tips for Upscayl options in settings 💁🏻‍♀️</li>
            <li>Added performance mode reminder for Windows users 🏇🏻</li>
            <li>Fix large image upscale performance bug 🐌</li>
            <li>Auto-update for MacOS, sorry for ignoring for so long 😅</li>
            <li>Fixed the annoying Upscayl Cloud bug. It won't appear again and again and you can make it disappear like it's MAGIC! 🪄</li>
          <li>Fix re-upscayl folder progress bug 🐞</li>
        </ul>
      </description>
    </release>
    <release version="v2.5.5" date="2023-06-04">
      <description>
        <p>Changes</p>
        <ul>
          <li>Added wiki option in the settings 📚</li>
          <li>Added warnings for Scale issue ⚠</li>
          <li>Fixed upscale resolution warning above 32k 🧐</li>
          <li>Fixed batch folder output folder bug 📁</li>
          <li>Disabled auto install on new updates ⚡</li>
        </ul>
      </description>
    </release>
    <release version="v2.5.1" date="2023-05-09">
      <description>
        <p>Changes</p>
        <ul>
          <li>New Settings tab!</li>
          <li>Upscayl now allows you to import your own models! (Refer GitHub Wiki)</li>
          <li>Reduced size since v2.0.1</li>
          <li>Updated Ultrasharp model</li>
          <li>Added FAST Real-ESRGAN Model</li>
          <li>A cancel upscaling button, finally!</li>
          <li>More visible progress bar</li>
          <li>New log area. Now you can directly see the logs and report bugs.</li>
          <li>Batch Mode is now persisted across sessions.</li>
          <li>Output Folder is now persisted across sessions.</li>
          <li>New experimental scale option.</li>
          <li>Removed sharpen option (it was useless tbh)</li>
          <li>Fixed issue with upscaling images with special characters in their name.</li>
          <li>Massively improved logging.</li>
          <li>Bug fixes</li>
          <li>Bug additions</li>
        </ul>
      </description>
    </release>
  </releases>
</component>
