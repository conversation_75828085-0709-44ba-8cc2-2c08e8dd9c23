---
title: Introduction
description: "Welcome to Upscayl Documentation! 🎉"
---

Here we're trying to build documentation around Upscayl for everyone.

This guide will continue to grow as we add more content. If you have any suggestions, please feel free to let us know on [GitHub Issues](https://github.com/upscayl/upscayl/issues) 🙏🏻

### Contents

<CardGroup cols={2}>
  <Card
    title="🙋 How to ask for help?"
    href="https://github.com/upscayl/upscayl/wiki/%F0%9F%99%8B-How-to-ask-for-help%3F"
  >
    Read this guide to learn how to ask for help on Upscayl's GitHub.
  </Card>
  <Card
    title="🧩 Compatibility List"
    href="https://github.com/upscayl/upscayl/wiki/Compatibility-List"
  >
    Check if your GPU is compatible with Upscayl.
  </Card>
  <Card
    title="📖 User Guide"
    href="https://github.com/upscayl/upscayl/wiki/Guide"
  >
    Learn the various confusing features of Upscayl.
  </Card>
  <Card
    title="🔨 Troubleshooting"
    href="/troubleshooting"
  >
    Learn how to troubleshoot common issues with Upscayl.
  </Card>
  <Card
    title="🖥️ Model Conversion Guide"
    href="https://github.com/upscayl/upscayl/wiki/Model-Conversion-Guide"
  >
    Learn how to convert PyTorch models to NCNN models for Upscayl.
  </Card>
  <Card
    title="🤫 Miscellaneous Configuration"
    href="https://github.com/upscayl/upscayl/wiki/Misc"
  >
    Learn about the miscellaneous configurations in Upscayl.
  </Card>
  <Card
    title="🤯 Using Upscayl on Windows and Linux with only a CPU"
    href="https://github.com/upscayl/upscayl/issues/390"
  >
    Learn how to use Upscayl on Windows and Linux without a graphics card.
  </Card>
</CardGroup>
