---
title: MacOS Troubleshooting
description: "Troubleshooting common issues with Upscayl on MacOS"
---

<Warning> 
If you have MacOS Monterey, you may face issues with the app (empty black screen). This is because Monterey had issues with electron apps, this isn't something we can fix as this is an OS level bug. We recommend upgrading to the latest version of MacOS that your device supports.
</Warning>

1. Make sure you're using the latest MacOS update.

2. If the app doesn't work, try deleting all its files and folders:
		1. Open Finder → Click **Go** in the menu bar → Select **Go to Folder** from the drop-down menu → In the window that appears, type **~/Library/** and press Enter. ![Finder Go To](images/mac-1.png)
		2. Here, in the `Library` folder, find and remove all files which contain `upscayl`. 
		3. Here's a list of files and folders you can safely delete:
			<Note>
			Please do not delete the folders that do not have `upscayl` in their name
			</Note>
			- ``~/Library/Application Support/Upscayl``
			- ``~/Library/Saved Application State/org.upscayl.Upscayl.savedState/``
			- ``~/Library/Group Containers/W2T4W74X87.org.upscayl.Upscayl``
			- ``~/Library/Containers/Upscayl`` and other folders there that have `upscayl` in their name.
			- ``~/Library/Preferences/org.upscayl.Upscayl.plist/``
			- ``~/Library/Preferences/org.upscayl.Upscayl.helper.plist/``
