{"id": "org.upscayl.Upscayl", "sdk": "org.freedesktop.Sdk", "runtime": "org.freedesktop.Platform", "runtime-version": "22.08", "base": "org.electronjs.Electron2.BaseApp", "base-version": "22.08", "command": "upscayl-run", "separate-locales": false, "finish-args": ["--device=dri", "--filesystem=home", "--share=network", "--socket=x11", "--socket=pulseaudio"], "modules": [{"name": "zypak", "sources": [{"type": "git", "url": "https://github.com/refi64/zypak", "tag": "v2022.04"}]}, {"name": "upscayl", "buildsystem": "simple", "sources": [{"type": "file", "only-arches": ["x86_64"], "url": "https://github.com/upscayl/upscayl/releases/download/v2.9.1/upscayl-2.9.1-linux.zip", "sha256": "238f1d9bebca8553b94572c6f9cf78b7838429f560842a90ad1f99ecda306c02"}, {"type": "file", "url": "https://raw.githubusercontent.com/upscayl/upscayl/main/flatpak/org.upscayl.Upscayl.metainfo.xml", "sha256": "e9fe8dc362f7b70f31c752c9c025b2f03447e17eaa93aa725a3a56b3ffc7893c"}, {"type": "script", "dest-filename": "upscayl-run", "commands": ["zypak-wrapper /app/upscayl/upscayl"]}], "build-commands": ["install -d /app/upscayl", "unzip upscayl-2.9.1-linux -d /app/upscayl", "install upscayl-run /app/bin/", "install -Dm644 /app/upscayl/icon_512x512.png /app/share/icons/hicolor/512x512/apps/org.upscayl.Upscayl.png", "install -Dm644 /app/upscayl/icon_128x128.png /app/share/icons/hicolor/128x128/apps/org.upscayl.Upscayl.png", "install -Dp -m644 org.upscayl.Upscayl.metainfo.xml /app/share/metainfo/org.upscayl.Upscayl.metainfo.xml", "touch /app/upscayl/org.upscayl.Upscayl.desktop", "desktop-file-edit --set-key=Exec --set-value='upscayl-run %U' --set-key=Icon --set-value='org.upscayl.Upscayl' --set-key=Comment --set-value='Upscale Images' --set-key=Name --set-value='Upscayl' --set-key=StartupNotify --set-value='false' --set-key=Categories --set-value='ImageProcessing;RasterGraphics;Graphics;' --set-key=Type --set-value='Application' /app/upscayl/org.upscayl.Upscayl.desktop", "install -Dm644 /app/upscayl/org.upscayl.Upscayl.desktop /app/share/applications/org.upscayl.Upscayl.desktop"]}]}