{"appId": "org.upscayl.Upscayl", "afterSign": "./notarize.js", "asar": true, "artifactName": "${name}-${version}-${os}.${ext}", "asarUnpack": ["**/node_modules/sharp/**/*"], "extraFiles": [{"from": "resources/${os}/bin", "to": "resources/bin", "filter": ["**/*"]}, {"from": "resources/models", "to": "resources/models", "filter": ["**/*"]}], "mac": {"type": "development", "hardenedRuntime": true, "gatekeeperAssess": false, "entitlements": "resources/entitlements.mac.plist", "entitlementsInherit": "resources/entitlements.mac.plist", "provisioningProfile": "dev.<PERSON>pro<PERSON>le", "mergeASARs": false, "x64ArchFiles": "*"}, "masDev": {"type": "development", "hardenedRuntime": false, "gatekeeperAssess": false, "electronLanguages": ["en"], "category": "public.app-category.photography", "entitlements": "resources/entitlements.mas-dev.plist", "provisioningProfile": "dev.<PERSON>pro<PERSON>le", "entitlementsInherit": "resources/entitlements.mas.inherit.plist", "icon": "build/icon.icns", "target": [{"target": "mas", "arch": ["universal"]}]}}