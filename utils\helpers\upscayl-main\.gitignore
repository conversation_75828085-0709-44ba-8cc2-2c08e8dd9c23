# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

/main/*.js
out/

# testing
/coverage

# next.js
/renderer/.next/
/renderer/out/

# production
/dist/
/export/electron
/export/common

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env.local
.env.development.local
.env.test.local
.env.production.local

# vercel
.vercel

#flatpak
.flatpak/
.flatpak-builder
repo/
build-dir/

#vscode
.vscode/

main/*.js
main/utils/*.js

.env
*.provisionprofile
flags.ts
*.p12
dockerfile