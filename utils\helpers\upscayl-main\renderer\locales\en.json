{"TITLE": "Upscayl", "INTRO": "Introducing Upscayl Cloud!", "HEADER": {"GITHUB_BUTTON_TITLE": "Star us on GitHub 😁", "DESCRIPTION": "AI Image Upscaler"}, "FOOTER": {"NEWS_TITLE": "UPSCAYL NEWS", "COPYRIGHT": "Copyright ©", "TITLE": "By ", "LINK_TITLE": "The Upscayl Team"}, "SETTINGS": {"TITLE": "Settings", "CHANGE_LANGUAGE": {"TITLE": "Change Language"}, "IMAGE_COMPRESSION": {"TITLE": "Image Compression", "DESCRIPTION": "PNG compression is lossless, so it might not reduce the file size significantly and higher compression values might affect the performance. JPG and WebP compression is lossy."}, "CUSTOM_MODELS": {"TITLE": "ADD CUSTOM MODELS", "BUTTON_FOLDER": "Select Folder", "DESCRIPTION": "You can add your own models easily. For more details:", "LINK_TITLE": "Custom Models Repository"}, "CUSTOM_INPUT_RESOLUTION": {"TITLE": "CUSTOM OUTPUT WIDTH", "RESTART": "REQUIRES RESTART", "DESCRIPTION": "Use a custom width for the output images. The height will be adjusted automatically. Enabling this will override the scale setting."}, "DONATE": {"DESCRIPTION": "If you like what we do :)", "BUTTON_TITLE": "💎 DONATE"}, "GPU_ID_INPUT": {"TITLE": "GPU ID", "DESCRIPTION": "Please read the Upscayl Documentation for more information.", "ADDITIONAL_DESCRIPTION": "Enable performance mode on Windows for better results."}, "IMAGE_FORMAT": {"TITLE": "SAVE IMAGE AS", "PNG": "PNG", "JPG": "JPG", "WEBP": "WEBP"}, "COPY_METADATA": {"TITLE": "COPY IMAGE METADATA", "DESCRIPTION": "Keep original image metadata (EXIF) in the upscaled image.", "SUGGEST_JPG_TITLE": "Recommended Format Change", "SUGGEST_JPG_DESCRIPTION": "For better metadata compatibility across different systems, we recommend using JPG format. Would you like to change?", "CHANGE_TO_JPG": "Change to JPG", "KEEP_CURRENT_FORMAT": "Keep {format}"}, "IMAGE_SCALE": {"TITLE": "Image Scale", "DESCRIPTION": "Anything above 4X (except 16X Double Upscayl) only resizes the image and does not use AI upscaling.", "WARNING": "Anything above 5X may cause performance issues on some devices!", "ADDITIONAL_WARNING": "This may cause performance issues on some devices!"}, "LOG_AREA": {"ON_COPY": "COPIED ✅", "BUTTON_TITLE": "COPY LOGS 📋", "NO_LOGS": "No logs to show"}, "OVERWRITE_TOGGLE": {"TITLE": "OVERWRITE PREVIOUS UPSCALE", "DESCRIPTION": "If enabled, Upscayl will process the image again instead of loading it directly."}, "RESET_SETTINGS": {"BUTTON_TITLE": "RESET UPSCAYL", "ALERT": "Upscayl has been reset. Please restart the app."}, "SAVE_OUTPUT_FOLDER": {"TITLE": "SAVE OUTPUT FOLDER", "DESCRIPTION": "If enabled, the output folder will be remembered between sessions."}, "AUTO_UPDATE": {"TITLE": "AUTO UPDATE UPSCAYL", "DESCRIPTION": "If enabled, the application will check for new updates and notify you."}, "ENABLE_CONTRIBUTION": {"TITLE": "HELP IMPROVE UPSCAYL", "DESCRIPTION": "If enabled, Upscayl will collect anonymous usage data to improve the application interface and features."}, "THEME": {"TITLE": "UPSCAYL THEME"}, "LANGUAGE": {"TITLE": "UPSCAYL LANGUAGE"}, "CUSTOM_TILE_SIZE": {"TITLE": "CUSTOM TILE SIZE", "DESCRIPTION": "Use a custom tile size for segmenting the image. This can help process images faster by reducing the number of tiles generated."}, "TURN_OFF_NOTIFICATIONS": {"TITLE": "TURN OFF NOTIFICATIONS", "DESCRIPTION": "If enabled, Upscayl will not send any system notifications on success or failure."}, "SUPPORT": {"TITLE": "Having issues?", "DOCS_BUTTON_TITLE": "🙏 GET HELP", "EMAIL_BUTTON_TITLE": "📧 EMAIL DEVELOPER"}, "TTA_MODE": {"TITLE": "TTA Mode", "DESCRIPTION": "Enable Test Time Augmentation for better results, such as removing artifacts BUT this will increase the processing time by 8x!"}, "SYSTEM_INFO": {"TITLE": "System Info"}}, "APP": {"TITLE": "Upscayl", "BATCH_MODE": {"TITLE": "<PERSON><PERSON>", "DESCRIPTION": "This will let you Upscayl all files in a folder at once"}, "FILE_SELECTION": {"TITLE": "Step 1", "BATCH_MODE_TYPE": "Select Folder", "SINGLE_MODE_TYPE": "Select Image"}, "MODEL_SELECTION": {"TITLE": "Step 2", "DESCRIPTION": "Select AI Model", "IMPORTED_CUSTOM_MODELS": "Imported Custom Models", "BEFORE": "Before", "AFTER": "After", "ZOOM": "Zoom", "MODELS": {"upscayl-standard-4x": {"NAME": "Upscayl Standard", "DESCRIPTION": "Suitable for most images."}, "upscayl-lite-4x": {"NAME": "Upscayl Lite", "DESCRIPTION": "Suitable for most images. High-speed upscaling with minimal quality loss."}, "remacri-4x": {"NAME": "<PERSON><PERSON><PERSON><PERSON> (Non-Commercial)", "DESCRIPTION": "For natural images. Added sharpness and detail. No commercial use."}, "ultramix-balanced-4x": {"NAME": "Ultramix (Non-Commercial)", "DESCRIPTION": "For natural images with a balance of sharpness and detail."}, "ultrasharp-4x": {"NAME": "Ultrasharp (Non-Commercial)", "DESCRIPTION": "For natural images with a focus on sharpness."}, "digital-art-4x": {"NAME": "Digital Art", "DESCRIPTION": "For digital art and illustrations."}, "high-fidelity-4x": {"NAME": "High Fidelity", "DESCRIPTION": "For all kinds of images with a focus on realistic details and smooth textures."}}}, "DOUBLE_UPSCAYL": {"TITLE": "Double Upscayl", "DESCRIPTION": "Enable this option to run upscayl twice on an image. Note that this may cause a significant increase in processing time and possibly performance issues for scales greater than 4X."}, "OUTPUT_PATH_SELECTION": {"TITLE": "Step 3", "MAC_APP_STORE_ALERT": "Due to MacOS App Store security restrictions, Upscayl requires you to select an output folder everytime you start it.\n\nTo avoid this, you can permanently save a default output folder in the Upscayl 'Settings' tab.", "NOT_SELECTED": "Not Selected", "DEFAULT_IMG_PATH": "Defaults to Image's path", "DEFAULT_FOLDER_PATH": "Defaults to  Folder's path", "BUTTON_TITLE": "Set Output Folder"}, "SCALE_SELECTION": {"TITLE": "Step 4", "FROM_TITLE": "Upscayl from ", "TO_TITLE": " to ", "NO_OUTPUT_FOLDER_ALERT": "Please select an output folder first", "START_BUTTON_TITLE": "Upscayl 🚀", "IN_PROGRESS_BUTTON_TITLE": "Upscayling ⏳"}, "MORE_OPTIONS_DRAWER": {"RESET_BUTTON_TITLE": "Reset Image", "LENS_VIEW_TITLE": "Lens View", "SLIDER_VIEW_TITLE": "Slider View", "ZOOM_AMOUNT_TITLE": "<PERSON><PERSON> Amount", "TOTAL_UPSCAYLS": "Total Upscayls", "TOTAL_BATCH_UPSCAYLS": "Total Batch Upscayls", "TOTAL_IMAGE_UPSCAYLS": "Total Image Upscayls", "TOTAL_DOUBLE_UPSCAYLS": "Total Double Upscayls", "AVERAGE_UPSCAYL_TIME": "Average Upscayl Time", "LAST_UPSCAYL_DURATION": "Last Upscayl Duration", "LAST_USED_AT": "Last Used At"}, "PROGRESS_BAR": {"BATCH_UPSCAYL_IN_PROGRESS_TITLE": "Batch Upscayl In Progress:", "IN_PROGRESS_TITLE": "Doing the Upscayl magic...", "STOP_BUTTON_TITLE": "STOP"}, "RESET_BUTTON_TITLE": "Reset", "RIGHT_PANE_INFO": {"SELECT_FOLDER": "Select a Folder", "SELECT_IMAGE": "Select an Image", "SELECT_FOLDER_DESCRIPTION": "Make sure that the folder doesn't contain anything except PNG, JPG, JPEG & WEBP images.", "SELECT_IMAGES_DESCRIPTION": "Select or drag and drop a PNG, JPG, JPEG or WEBP image.", "PASTE_IMAGE_DESCRIPTION": "Hit Ctrl+V or ⌘+V to Paste image from Clipboard"}, "PROGRESS": {"PROCESSING_TITLE": "Processing the image...", "SCALING_CONVERTING_TITLE": "Scaling and converting image...", "WAIT_TITLE": "Hold on...", "SUCCESS_TITLE": "Upscayl Successful!", "BATCH": {"SELECTED_FOLDER_TITLE": "Selected folder:", "DONE_TITLE": "All done!", "OPEN_UPSCAYLED_FOLDER_TITLE": "Open Upscayled Folder"}}, "SLIDER": {"ORIGINAL_TITLE": "Original", "UPSCAYLED_TITLE": "Upscayled"}, "DIALOG_BOX": {"CLOSE": "Close"}}, "ERRORS": {"GPU_ERROR": {"TITLE": "GPU Error", "DESCRIPTION": "Ran into an issue with the GPU. Please read the docs for troubleshooting! ({data})"}, "COPY_ERROR": {"TITLE": "<PERSON><PERSON>", "DESCRIPTION": ""}, "READ_WRITE_ERROR": {"TITLE": "Read/Write Error", "DESCRIPTION": "Make sure that the path is correct and you have proper read/write permissions \n({data})"}, "TILE_SIZE_ERROR": {"TITLE": "Error", "DESCRIPTION": "The tile size is wrong. Please change the tile size in the settings or set to 0 ({data})"}, "EXCEPTION_ERROR": {"TITLE": "Exception Error", "DESCRIPTION": "Upscayl encountered an error. Possibly, the upscayl binary failed to execute the commands properly. Try checking the logs to see if you get any information. You can post an issue on Upscayl's GitHub repository for more help."}, "GENERIC_ERROR": {"TITLE": "Error"}, "NO_OUTPUT_FOLDER_ERROR": {"TITLE": "Set Output Folder", "DESCRIPTION": "Please select an output folder first"}, "INVALID_IMAGE_ERROR": {"TITLE": "Invalid Image", "DESCRIPTION": "Please select/paste an image with a valid extension like PNG, JPG, JPEG, JFIF or WEBP.", "ADDITIONAL_DESCRIPTION": "Please drag and drop an image", "CLIPBOARD_DESCRIPTION": "No Image file found in Clipboard to paste!"}, "NO_IMAGE_ERROR": {"TITLE": "No image selected", "DESCRIPTION": "Please select an image to upscale"}, "METADATA_ERROR": {"TITLE": "Metadata <PERSON>", "DESCRIPTION": "Could not copy metadata from the original image to the generated image. ({data})"}, "OPEN_DOCS_TITLE": "Open Docs", "OPEN_DOCS_BUTTON_TITLE": "Troubleshoot"}, "WARNING": {"GENERIC_WARNING": {"TITLE": "Warning"}, "METADATA_FORMAT": {"TITLE": "Metadata Warning", "DESCRIPTION": "On some systems, metadata may not be visible in formats other than JPG. Use specific tools to view metadata if needed."}}, "UPSCAYL_CLOUD": {"COMING_SOON": "Coming soon!", "CATCHY_PHRASE_1": "No more errors, hardware issues, quality compromises or long loading times!", "CATCHY_PHRASE_2": "🌐 Upscayl anywhere, anytime, any device\n☁️ No Graphics Card or hardware required\n👩 Face Enhancement\n🦋 10+ models to choose from\n🏎 5x faster than Upscayl Desktop\n🎞 Video Upscaling\n💰 Commercial Usage\n😴 Upscayl while you sleep", "ALREADY_REGISTERED_ALERT": "Thank you {name}! It seems that your email has already been registered :D If that's not the case, please try again.", "ADD_SUCCESS": "Thank you for joining the waitlist! We will notify you when Upscayl Cloud is ready for you.", "INCORRECT_FIELDS_ALERT": "Please fill in all the fields correctly.", "JOIN_WAITLIST": "Join the waitlist", "DONT_SHOW_AGAIN": "DON'T SHOW AGAIN"}, "ONBOARDING_DIALOG": {"NEXT_BUTTON_TITLE": "Next", "BACK_BUTTON_TITLE": "Back", "GET_STARTED_BUTTON_TITLE": "Get Started", "SETTINGS_NOTE": "You can always change these settings later.", "STEP_1": {"TITLE": "Welcome to Upscayl 🎉", "DESCRIPTION": "Let's get you started with a few quick steps."}, "STEP_2": {"TITLE": "Choose Your Preferences 🎨", "DESCRIPTION": "Configure your initial settings."}, "STEP_3": {"TITLE": "How do I use Upscayl? 🚀", "DESCRIPTION": "Watch this short video to learn about the new features."}, "STEP_4": {"TITLE": "You're All Set! 🎉", "DESCRIPTION": "You're ready to start upscaling images with Upscayl!"}}}