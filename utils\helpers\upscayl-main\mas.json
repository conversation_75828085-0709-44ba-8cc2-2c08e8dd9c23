{"productName": "Upscayl", "appId": "org.upscayl.Upscayl", "afterSign": "./notarize.js", "buildVersion": "25.12.26", "asar": true, "asarUnpack": ["**/node_modules/sharp/**/*", "**/node_modules/@img/**/*"], "extraFiles": [{"from": "resources/${os}/bin", "to": "resources/bin", "filter": ["**/*"]}, {"from": "resources/models", "to": "resources/models", "filter": ["**/*"]}], "mas": {"hardenedRuntime": false, "gatekeeperAssess": false, "type": "distribution", "category": "public.app-category.photography", "entitlements": "resources/entitlements.mas.plist", "entitlementsInherit": "resources/entitlements.mas.inherit.plist", "provisioningProfile": "embedded.provisionprofile", "icon": "build/icon.icns", "x64ArchFiles": "*", "target": [{"target": "mas", "arch": ["universal"]}]}, "mac": {"hardenedRuntime": true, "gatekeeperAssess": false, "mergeASARs": false, "minimumSystemVersion": "12.0.0", "electronLanguages": ["en"], "x64ArchFiles": "*", "type": "distribution", "icon": "build/icon.icns", "category": "public.app-category.photography", "entitlements": "resources/entitlements.mac.plist", "entitlementsInherit": "resources/entitlements.mac.plist", "provisioningProfile": "embedded.provisionprofile"}}