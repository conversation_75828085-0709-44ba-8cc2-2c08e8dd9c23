{"name": "ImageUpscaling", "description": "Image upscaling using multiple state-of-the-art models (SwinIR, Real-ESRGAN, UltraSharp, etc.)", "dependencies": {"python": ["torch>=2.0.0", "torchvision>=0.15.0", "pillow>=10.0.0", "numpy<2.0.0", "opencv-python>=4.8.0", "timm>=0.9.0", "einops>=0.6.0", "requests>=2.25.0", "tqdm>=4.64.0", "hf_transfer>=0.1.4"], "models": [{"name": "swinir-real-sr-x4", "repo_id": "valhalla/SwinIR-real-sr-L-x4-GAN", "required": true, "description": "SwinIR Real-World Super-Resolution x4 - High quality upscaler", "local_path": "ImageUpscaling/swinir-real-sr-x4", "files": ["003_realSR_BSRGAN_DFOWMFC_s64w8_SwinIR-L_x4_GAN.pth"]}, {"name": "realesrgan-x4plus", "repo_id": "xinntao/Real-ESRGAN", "required": false, "description": "Real-ESRGAN x4plus - General purpose upscaler (photo, art, etc.)", "local_path": "ImageUpscaling/realesrgan-x4plus", "files": ["RealESRGAN_x4plus.pth"]}, {"name": "realesrgan-x4plus-anime", "repo_id": "xinntao/Real-ESRGAN", "required": false, "description": "Real-ESRGAN x4plus anime - Anime/illustration upscaler", "local_path": "ImageUpscaling/realesrgan-x4plus-anime", "files": ["RealESRGAN_x4plus_anime_6B.pth"]}, {"name": "swinir-m-x4", "repo_id": "JingyunLiang/SwinIR", "required": false, "description": "SwinIR-M x4 - Medium SwinIR model for classical super-resolution", "local_path": "ImageUpscaling/swinir-m-x4", "files": ["001_classicalSR_DF2K_s64w8_SwinIR-M_x4.pth"]}, {"name": "4xultrasharp", "repo_id": "upscayl/UltraSharp", "required": false, "description": "4xUltraSharp - Ultra-sharp upscaler for crisp details (from Upscayl)", "local_path": "ImageUpscaling/4xultrasharp", "files": ["4x-UltraSharp.pth"]}]}}