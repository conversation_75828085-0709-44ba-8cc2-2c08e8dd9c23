#!/usr/bin/env python3
"""
Simple upscaling script using only PIL - no PyTorch required
"""
import argparse
import os
import sys

# Force stdout to be line-buffered
sys.stdout.reconfigure(line_buffering=True)
sys.stderr.reconfigure(line_buffering=True)

print("PROGRESS:{\"stage\": \"startup\", \"step\": 1, \"total\": 5, \"progress\": 20, \"message\": \"Starting simple upscaling...\"}", flush=True)

try:
    from PIL import Image
    print("PROGRESS:{\"stage\": \"startup\", \"step\": 2, \"total\": 5, \"progress\": 40, \"message\": \"PIL imported successfully\"}", flush=True)
except ImportError as e:
    print(f"PROGRESS:{{\"stage\": \"error\", \"step\": 0, \"total\": 5, \"progress\": 0, \"message\": \"Failed to import PIL: {e}\"}}", flush=True)
    sys.exit(1)

def main():
    parser = argparse.ArgumentParser(description='Simple image upscaling using PIL')
    parser.add_argument('--input', required=True, help='Input image path')
    parser.add_argument('--output', required=True, help='Output image path')
    parser.add_argument('--model', required=True, help='Model name (ignored for simple upscaling)')
    parser.add_argument('--scale', type=int, default=4, help='Upscaling factor')
    parser.add_argument('--format', default='png', help='Output format')
    parser.add_argument('--compression', type=int, default=0, help='Compression level')
    parser.add_argument('--custom-width', type=int, help='Custom output width')
    parser.add_argument('--tile-size', type=int, default=400, help='Tile size (ignored)')
    parser.add_argument('--tta-mode', action='store_true', help='TTA mode (ignored)')
    parser.add_argument('--gpu-id', help='GPU ID (ignored)')
    
    args = parser.parse_args()
    
    try:
        print("PROGRESS:{\"stage\": \"loading\", \"step\": 3, \"total\": 5, \"progress\": 60, \"message\": \"Loading input image...\"}", flush=True)
        
        # Load image
        img = Image.open(args.input).convert('RGB')
        print(f"Input image size: {img.size}")
        
        print("PROGRESS:{\"stage\": \"upscaling\", \"step\": 4, \"total\": 5, \"progress\": 80, \"message\": \"Upscaling image...\"}", flush=True)
        
        # Simple upscaling using LANCZOS
        if args.custom_width and args.custom_width > 0:
            # Use custom width
            aspect_ratio = img.height / img.width
            new_height = int(args.custom_width * aspect_ratio)
            upscaled = img.resize((args.custom_width, new_height), Image.LANCZOS)
        else:
            # Use scale factor
            new_width = img.width * args.scale
            new_height = img.height * args.scale
            upscaled = img.resize((new_width, new_height), Image.LANCZOS)
        
        print(f"Output image size: {upscaled.size}")
        
        # Save image
        output_dir = os.path.dirname(args.output)
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
        
        save_kwargs = {}
        if args.format.lower() == 'jpeg' or args.format.lower() == 'jpg':
            save_kwargs['quality'] = max(10, 100 - args.compression)
            save_kwargs['optimize'] = True
        
        upscaled.save(args.output, format=args.format.upper(), **save_kwargs)
        
        # Verify file was saved
        if os.path.exists(args.output):
            file_size = os.path.getsize(args.output)
            print("PROGRESS:{\"stage\": \"complete\", \"step\": 5, \"total\": 5, \"progress\": 100, \"message\": \"Upscaling completed successfully\"}", flush=True)
            print(f"Saved to: {args.output} ({file_size} bytes)")
        else:
            raise RuntimeError(f"Failed to save output file: {args.output}")
            
    except Exception as e:
        print(f"PROGRESS:{{\"stage\": \"error\", \"step\": 0, \"total\": 5, \"progress\": 0, \"message\": \"Error: {str(e)}\"}}", flush=True)
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()
