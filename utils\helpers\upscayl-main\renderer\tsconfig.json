{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": true, "noEmit": true, "incremental": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "paths": {"@/*": ["./*"], "@common/*": ["../common/*"], "@components/*": ["./components/*"], "@lib/utils": ["./lib/utils/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "../app.module.ts", "../common/**/*"], "exclude": ["node_modules"]}