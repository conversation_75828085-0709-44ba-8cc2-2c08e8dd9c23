const ELECTRON_COMMANDS = {
  SELECT_FILE: "Select a File",
  SELECT_FOLDER: "Select a Folder",
  UPSCAYL: "Upscale the Image",
  UPSCAYL_DONE: "Upscaling Done",
  UPSCAYL_PROGRESS: "Send Progress from Main to Renderer",
  DOUBLE_UPSCAYL: "Double Upscale the Image",
  DOUBLE_UPSCAYL_DONE: "Double Upscaling Done",
  DOUBLE_UPSCAYL_PROGRESS: "Send Double Upscayl Progress from Main to Renderer",
  FOLDER_UPSCAYL: "Upscale a Folder",
  FOLDER_UPSCAYL_DONE: "Folder upscaling successful",
  FOLDER_UPSCAYL_PROGRESS:
    "Send Folder Upscaling Progress from Main to Renderer",
  OPEN_FOLDER: "Open Folder",
  UPSCAYL_VIDEO: "Upscale the Video",
  UPSCAYL_VIDEO_DONE: "Video Upscaling Done",
  UPSCAYL_VIDEO_PROGRESS: "Send Video Upscale Progress from Main to Renderer",
  FFMPEG_VIDEO_DONE: "Ran FFMpeg successfully",
  FFMPEG_VIDEO_PROGRESS: "Running FFMpeg for frame extraction",
  SELECT_CUSTOM_MODEL_FOLDER: "Select a Custom Model Folder",
  GET_MODELS_LIST: "Send models list from main to renderer",
  CUSTOM_MODEL_FILES_LIST: "Send custom model files list to renderer",
  LOG: "Log",
  STOP: "Stop the current operation",
  OS: "Get OS",
  SCALING_AND_CONVERTING: "Adding some finishing touches",
  UPSCAYL_WARNING: "Upscaling Warning",
  UPSCAYL_ERROR: "Upscaling Error",
  METADATA_ERROR: "Metadata Error",
  PASTE_IMAGE: "Paste Image from clipboard",
  PASTE_IMAGE_SAVE_SUCCESS: "Clipboard Image saved successfully",
  PASTE_IMAGE_SAVE_ERROR: "Clipboard Image save failed",
} as const;

export { ELECTRON_COMMANDS };
